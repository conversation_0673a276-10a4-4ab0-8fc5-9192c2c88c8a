/**
 * 提示词模块入口文件
 * 统一构建和导出所有提示词
 */
import { KEYWORD_TYPES } from '../strategies.js'
import { formatPromptTemplate, addImageExtension, CONFIG } from './common.js'
import { log } from '../../utils/logging.js'

// ==================== 策略创建与配置 ====================

/**
 * 创建一个标准的提示词构建策略。
 * @param {string[]} promptKeys - 需要的模板键列表
 * @param {boolean} useCropsInfo - 是否使用作物信息
 * @param {Function} prepareContent - 内容准备函数
 * @returns {object} 一个策略对象
 */
const createStrategy = (promptKeys, useCropsInfo, prepareContent) => ({
  promptKeys,
  useCropsInfo,
  prepareContent,
})

/**
 * 定义每种提示词类型的构建策略。
 * 这是本模块的核心，清晰地描述了每种类型的独特需求。
 */
const PROMPT_STRATEGIES = {
  [KEYWORD_TYPES.NORMAL]: createStrategy(['normal_prompt'], true, contents => contents[0] || ''),
  [KEYWORD_TYPES.TRANSPORT]: createStrategy(
    ['transport_prompt'],
    false,
    contents => contents[0] || '',
  ),
  [KEYWORD_TYPES.UREA]: createStrategy(
    ['urea_prompt', 'normal_prompt'],
    true,
    ([urea, normal]) => urea || normal || '', // 优先用尿素模板
  ),
  [KEYWORD_TYPES.DETAILS_ONLY]: createStrategy(
    ['details_only_prompt'],
    true,
    contents => contents[0] || '',
  ),
  [KEYWORD_TYPES.ARTICLE]: createStrategy(
    ['article_prompt', 'normal_prompt'],
    true,
    contents => contents.filter(Boolean).join('\n'), // 拼接有效内容
  ),
  [KEYWORD_TYPES.IMPORTED]: createStrategy(
    ['normal_prompt', 'imported_prompt'],
    true,
    contents => contents.filter(Boolean).join('\n'), // 拼接有效内容
  ),
}

// ==================== 统一构建函数 ====================

/**
 * 构建最终的AI提示词。
 * 这是一个统一的、配置驱动的函数，取代了之前分散的多个builder。
 * @param {string} keywordType - 关键词类型
 * @param {boolean} isImage - 是否为图片处理
 * @param {string} currentDate - 当前日期
 * @param {Array} cropsInfo - 作物信息
 * @param {Object} allPrompts - 包含所有已加载模板的对象
 * @returns {string} 格式化后的最终提示词
 */
function buildPrompt(keywordType, isImage, currentDate, cropsInfo = [], allPrompts = {}) {
  // 1. 根据关键词类型，获取对应的构建策略
  const strategy = PROMPT_STRATEGIES[keywordType]

  // 如果没有找到策略，返回一个安全的默认提示词
  if (!strategy) {
    log.c.warn(`未找到关键词类型 "${keywordType}" 的提示词构建策略，使用默认提示词。`)
    return '请分析以下内容，提取关键信息，并输出JSON格式的结构化数据。'
  }
  // 2. 从总的提示词对象中，提取当前策略所需的模板内容
  const { promptKeys, useCropsInfo, prepareContent } = strategy
  const requiredContents = promptKeys.map(key => allPrompts[key] || '')

  // 3. 使用策略中定义的prepareContent方法，准备基础提示词内容
  const baseContent = prepareContent(requiredContents)

  // 4. 格式化基础提示词，注入动态数据
  // 注意：只有当策略明确需要时，才传递cropsInfo
  const formattingOptions = useCropsInfo ? { cropsInfo } : {}
  const basePrompt = formatPromptTemplate(baseContent, currentDate, cropsInfo, formattingOptions)

  // 5. 如果是图片，附加图片处理的特定指令
  if (isImage) {
    const imageExtension = allPrompts['image_extension'] || ''
    return addImageExtension(imageExtension, basePrompt)
  }

  return basePrompt
}

// ==================== 导出模块 ====================

export { buildPrompt, CONFIG }
