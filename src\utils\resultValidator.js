/**
 * AI结果验证和后处理模块
 * 提供去重、数据验证等后处理功能，确保AI输出的质量
 */

import { log } from './logging.js'
import { isValidString, isValidNumber, isNonEmptyArray } from './dataValidators.js'
import { PRIORITY_CONFIGS } from '../core/prompts/common.js'
import { KEYWORD_TYPES } from '../core/strategies.js'

/**
 * 将数组格式的权重配置转换为验证器所需的旧版对象格式。
 * @param {Array<Object>} configArray - 来自 PRIORITY_CONFIGS 的配置数组
 * @returns {Object} 转换后的权重配置对象
 */
const convertPriorityConfig = configArray => {
  const keyMap = {
    水分条件: 'moistureConditions',
    厂区类型: 'factoryTypes',
    价格类型: 'priceTypes',
    作物品质: 'cropQualities',
    票据状态: 'ticketStatus',
  }
  return configArray.reduce((acc, { title, weights }) => {
    const key = keyMap[title]
    if (key) {
      acc[key] = weights
    }
    return acc
  }, {})
}

/**
 * 优先级配置 - 从新的 PRIORITY_CONFIGS.NORMAL 动态生成
 */
const PRIORITY_CONFIG = convertPriorityConfig(PRIORITY_CONFIGS.NORMAL || [])

const DATA_FIELDS = {
  transport: ['factoryName', 'yesterdayData', 'todayData', 'date'],
  normal: ['company', 'crop', 'price', 'unit', 'date', 'detail'],
  details_only: ['company', 'crop', 'date', 'detail'],
  imported: ['company', 'crop', 'price', 'unit', 'date', 'detail'],
  urea: ['company', 'crop', 'price', 'unit', 'date', 'detail'],
  article: ['company', 'crop', 'price', 'unit', 'date', 'detail'],
}

// 字段类型验证规则
const FIELD_VALIDATORS = {
  // 字符串字段验证
  string: (value, fieldName) => {
    if (!isValidString(value)) {
      return `${fieldName}必须是有效的非空字符串`
    }
    return null
  },

  // 数字字段验证
  number: (value, fieldName) => {
    if (!isValidNumber(value)) {
      return `${fieldName}必须是有效数字`
    }
    if (value < 0) {
      return `${fieldName}不能为负数`
    }
    return null
  },

  // 价格字段验证（必须大于0）
  price: (value, fieldName) => {
    if (!isValidNumber(value)) {
      return `${fieldName}必须是有效数字`
    }
    if (value <= 0) {
      return `${fieldName}必须大于0`
    }
    return null
  },

  // 日期字段验证
  date: (value, fieldName) => {
    if (!isValidString(value)) {
      return `${fieldName}必须是有效的日期字符串`
    }
    // 简单的日期格式验证
    const datePattern = /^\d{4}-\d{2}-\d{2}$/
    if (!datePattern.test(value)) {
      return `${fieldName}格式应为YYYY-MM-DD`
    }
    return null
  },

  // 单位字段验证
  unit: (value, fieldName) => {
    if (!isValidString(value)) {
      return `${fieldName}必须是有效的单位字符串`
    }
    const validUnits = ['元/斤', '元/吨', '元/公斤']
    if (!validUnits.includes(value)) {
      return `${fieldName}必须是有效的价格单位: ${validUnits.join(', ')}`
    }
    return null
  },
}

// 不同提示词类型的字段类型定义
const FIELD_TYPES = {
  transport: {
    factoryName: 'string',
    yesterdayData: 'number',
    todayData: 'number',
    date: 'date',
  },
  normal: {
    company: 'string',
    crop: 'string',
    price: 'price',
    unit: 'unit',
    date: 'date',
    detail: 'string',
  },
  details_only: {
    company: 'string',
    crop: 'string',
    date: 'date',
    detail: 'string',
  },
  imported: {
    company: 'string',
    crop: 'string',
    price: 'price',
    unit: 'unit',
    date: 'date',
    detail: 'string',
  },
  urea: {
    company: 'string',
    crop: 'string',
    price: 'price',
    unit: 'unit',
    date: 'date',
    detail: 'string',
  },
  article: {
    company: 'string',
    crop: 'string',
    price: 'price',
    unit: 'unit',
    date: 'date',
    detail: 'string',
  },
}

/**
 * 结果验证器类
 */
class ResultValidator {
  /**
   * 验证并处理AI返回的结果
   * @param {Array} results - AI返回的结果数组
   * @param {Object} options - 验证选项
   * @returns {Array} 验证和去重后的结果
   */
  static validateAndProcess(results, options = {}) {
    if (!isNonEmptyArray(results)) {
      return []
    }

    try {
      // 1. 基础验证
      const validResults = this.validateBasicFields(results, options.promptType)

      const noDeduplicationTypes = [KEYWORD_TYPES.TRANSPORT, KEYWORD_TYPES.DETAILS_ONLY]
      if (noDeduplicationTypes.includes(options.promptType)) {
        log.c.info(`结果验证完成: 输出${validResults.length}条`)
        return validResults
      }

      // 2. 去重处理（根据提示词类型决定是否去重）
      const deduplicatedResults = this.deduplicateResults(validResults, options.promptType)

      // 3. 最终验证
      const finalResults = this.finalValidation(deduplicatedResults)

      log.c.info(`结果验证完成: 输入${results.length}条 → 输出${finalResults.length}条`)

      return finalResults
    } catch (error) {
      log.c.error(`结果验证失败: ${error.message}`)
      return []
    }
  }

  /**
   * 基础字段验证
   * @param {Array} results - 结果数组
   * @param {string} promptType - 提示词类型
   * @returns {Array} 验证通过的结果
   */
  static validateBasicFields(results, promptType) {
    if (!isNonEmptyArray(results)) {
      log.c.warn('输入结果为空或无效')
      return []
    }

    if (!DATA_FIELDS[promptType]) {
      log.c.error(`未知的提示词类型: ${promptType}`)
      return []
    }

    const requiredFields = DATA_FIELDS[promptType]
    const fieldTypes = FIELD_TYPES[promptType]
    const validationStats = {
      total: results.length,
      passed: 0,
      failed: 0,
      errors: [],
    }

    const validResults = results.filter((item, index) => {
      const recordIndex = index + 1

      // 基础对象验证
      if (!item || typeof item !== 'object') {
        const error = `记录${recordIndex}不是有效的对象`
        log.c.warn(error, item)
        validationStats.errors.push(error)
        validationStats.failed++
        return false
      }

      // 检查必填字段存在性
      for (const field of requiredFields) {
        if (item[field] === undefined || item[field] === null || item[field] === '') {
          const error = `记录${recordIndex}缺少必填字段 [${field}]`
          log.c.warn(error, item)
          validationStats.errors.push(error)
          validationStats.failed++
          return false
        }
      }

      // 检查字段类型和值的有效性
      for (const [field, expectedType] of Object.entries(fieldTypes)) {
        if (item[field] !== undefined) {
          const validator = FIELD_VALIDATORS[expectedType]
          if (validator) {
            const validationError = validator(item[field], field)
            if (validationError) {
              const error = `记录${recordIndex} ${validationError}`
              log.c.warn(error, { field, value: item[field], item })
              validationStats.errors.push(error)
              validationStats.failed++
              return false
            }
          }
        }
      }

      // 特殊业务逻辑验证
      if (!this.validateBusinessLogic(item, promptType, recordIndex)) {
        validationStats.failed++
        return false
      }

      validationStats.passed++
      return true
    })

    // 输出验证统计信息
    log.c.info(
      `字段验证完成 - 类型: ${promptType}, 总数: ${validationStats.total}, 通过: ${validationStats.passed}, 失败: ${validationStats.failed}`,
    )

    if (validationStats.failed > 0) {
      log.c.warn(`验证失败的记录数: ${validationStats.failed}`, {
        promptType,
        errorSummary: validationStats.errors.slice(0, 5), // 只显示前5个错误
      })
    }

    return validResults
  }

  /**
   * 业务逻辑验证
   * @param {Object} item - 单条记录
   * @param {string} promptType - 提示词类型
   * @param {number} recordIndex - 记录索引（用于日志）
   * @returns {boolean} 验证是否通过
   */
  static validateBusinessLogic(item, promptType, recordIndex) {
    try {
      // 运输数据特殊验证
      if (promptType === KEYWORD_TYPES.TRANSPORT) {
        // 验证数据的合理性
        if (item.yesterdayData < 0 || item.todayData < 0) {
          log.c.warn(`记录${recordIndex}: 运输数据不能为负数`, item)
          return false
        }

        // 验证数据的合理范围（可根据业务需求调整）
        const maxReasonableValue = 10000
        if (item.yesterdayData > maxReasonableValue || item.todayData > maxReasonableValue) {
          log.c.warn(`记录${recordIndex}: 运输数据超出合理范围`, item)
          return false
        }
      } else {
        // 价格相关数据验证
        if (item.price) {
          // 价格合理性验证
          const minPrice = item.unit === '元/吨' ? 100 : 0.1
          const maxPrice = item.unit === '元/吨' ? 10000 : 50

          if (item.price < minPrice || item.price > maxPrice) {
            log.c.warn(
              `记录${recordIndex}: 价格 ${item.price} ${item.unit} 超出合理范围 [${minPrice}-${maxPrice}]`,
              item,
            )
            return false
          }
        }

        // 公司名称合理性验证
        if (item.company) {
          if (item.company.length < 2 || item.company.length > 50) {
            log.c.warn(`记录${recordIndex}: 公司名称长度不合理`, item)
            return false
          }

          // 检查是否包含明显的无效字符
          const invalidPatterns = [/^\s*$/, /^[0-9]+$/, /^[^\u4e00-\u9fa5a-zA-Z]+$/]
          if (invalidPatterns.some(pattern => pattern.test(item.company))) {
            log.c.warn(`记录${recordIndex}: 公司名称格式无效`, item)
            return false
          }
        }

        // 作物名称验证
        if (item.crop) {
          const validCrops = ['小麦', '玉米', '大豆', '水稻', '高粱', '豆粕', '尿素']
          if (!validCrops.some(crop => item.crop.includes(crop))) {
            log.c.warn(`记录${recordIndex}: 作物名称可能无效: ${item.crop}`, item)
            // 注意：这里只是警告，不直接返回false，因为可能有新的作物类型
          }
        }
      }

      return true
    } catch (error) {
      log.c.error(`记录${recordIndex}: 业务逻辑验证失败: ${error.message}`, item)
      return false
    }
  }

  /**
   * 去重处理 - 根据提示词类型决定是否去重
   * @param {Array} results - 验证后的结果数组
   * @param {string} promptType - 提示词类型
   * @returns {Array} 去重后的结果
   */
  static deduplicateResults(results, promptType) {
    if (!isNonEmptyArray(results)) {
      return []
    }

    log.c.info(`开始去重处理，输入 ${results.length} 条记录`)

    // 按公司-作物组合分组
    const groups = new Map()

    results.forEach(item => {
      const key = `${item.company}|${item.crop}`
      if (!groups.has(key)) {
        groups.set(key, [])
      }
      groups.get(key).push(item)
    })

    // 对每个分组选择最优记录
    const deduplicatedResults = []

    groups.forEach((groupItems, key) => {
      if (groupItems.length === 1) {
        deduplicatedResults.push(groupItems[0])
      } else {
        // 多条记录，需要选择最优的
        const bestItem = this.selectBestRecord(groupItems)

        deduplicatedResults.push(bestItem)

        log.c.warn(`发现重复记录 ${key}: ${groupItems.length}条 → 选择最优1条`)
      }
    })

    const duplicateCount = results.length - deduplicatedResults.length
    if (duplicateCount > 0) {
      log.c.info(
        `去重完成：移除 ${duplicateCount} 条重复记录，保留 ${deduplicatedResults.length} 条记录`,
      )
    } else {
      log.c.info(`去重完成：无重复记录，保留所有 ${deduplicatedResults.length} 条记录`)
    }

    return deduplicatedResults
  }

  /**
   * 从多条记录中选择最优记录
   * @param {Array} records - 同一公司-作物的多条记录
   * @returns {Object} 最优记录
   */
  static selectBestRecord(records) {
    if (records.length === 1) {
      return records[0]
    }

    // 计算每条记录的优先级分数
    const scoredRecords = records.map(record => ({
      ...record,
      _priorityScore: this.calculatePriorityScore(record),
    }))

    // 按优先级分数排序
    scoredRecords.sort((a, b) => {
      if (b._priorityScore !== a._priorityScore) {
        return b._priorityScore - a._priorityScore
      }
      // 分数相同时选择价格较高的
      return b.price - a.price
    })

    // 移除临时的优先级分数字段
    const bestRecord = { ...scoredRecords[0] }
    delete bestRecord._priorityScore

    return bestRecord
  }

  /**
   * 计算记录的优先级分数
   * @param {Object} record - 记录对象
   * @returns {number} 优先级分数
   */
  static calculatePriorityScore(record) {
    let score = 0
    const detail = record.detail || ''
    const crop = record.crop || ''

    // 移除独立的小麦品类处理，已合并到作物品质优先级中

    // 水分条件分数
    for (const [condition, points] of Object.entries(PRIORITY_CONFIG.moistureConditions)) {
      if (
        detail.includes(condition) ||
        detail.includes(condition.replace('≤', '<=').replace('>', '>'))
      ) {
        score += points
        break
      }
    }

    // 厂区类型分数
    for (const [type, points] of Object.entries(PRIORITY_CONFIG.factoryTypes)) {
      if (detail.includes(type)) {
        score += points
        break
      }
    }

    // 价格类型分数
    for (const [type, points] of Object.entries(PRIORITY_CONFIG.priceTypes)) {
      if (detail.includes(type)) {
        score += points
        break
      }
    }

    // 作物品质分数
    for (const [quality, points] of Object.entries(PRIORITY_CONFIG.cropQualities)) {
      if (detail.includes(quality)) {
        score += points
        break
      }
    }

    // 票据状态分数
    for (const [status, points] of Object.entries(PRIORITY_CONFIG.ticketStatus)) {
      if (detail.includes(status)) {
        score += points
        break
      }
    }

    return score
  }

  /**
   * 最终验证
   * @param {Array} results - 去重后的结果
   * @returns {Array} 最终验证通过的结果
   */
  static finalValidation(results) {
    // 再次检查唯一性
    const uniqueKeys = new Set()
    const finalResults = []

    for (const item of results) {
      const key = `${item.company}|${item.crop}`
      if (uniqueKeys.has(key)) {
        log.c.error(`最终验证发现重复记录: ${key}`)
        continue
      }
      uniqueKeys.add(key)
      finalResults.push(item)
    }

    return finalResults
  }

  /**
   * 验证结果的唯一性
   * @param {Array} results - 结果数组
   * @returns {boolean} 是否通过唯一性验证
   */
  static validateUniqueness(results) {
    const keys = new Set()

    for (const item of results) {
      const key = `${item.company}|${item.crop}`
      if (keys.has(key)) {
        return false
      }
      keys.add(key)
    }

    return true
  }
}

export default ResultValidator
