/**
 * 统一日志系统
 */

import { createLogger, format, transports, addColors } from 'winston'
import 'winston-daily-rotate-file'
import path from 'path'
import fs from 'fs'
import chalk from 'chalk'
import config from '../config/index.js'
import { FORMATS } from './dateUtils.js'

const { combine, timestamp, printf, colorize, errors } = format

// 确保日志目录存在
if (!fs.existsSync(config.paths.logs)) {
  fs.mkdirSync(config.paths.logs, { recursive: true })
}

// 日志配置
const LOG_CONFIG = {
  levels: { error: 0, warn: 1, success: 2, info: 3, debug: 4 },
  colors: { error: 'red', warn: 'yellow', success: 'green', info: 'cyan', debug: 'gray' },
  chalkColors: {
    error: chalk.red,
    warn: chalk.yellow,
    success: chalk.green,
    info: chalk.cyan,
    debug: chalk.gray,
  },
}

addColors(LOG_CONFIG.colors)

// 公共工具函数
const serializeMeta = meta => {
  if (!meta || !Object.keys(meta).length) return ''
  try {
    return JSON.stringify(meta, null, 2)
  } catch {
    return '[meta序列化失败]'
  }
}

const formatLogMessage = (timestamp, level, message, meta) => {
  const metaStr = serializeMeta(meta)
  return `${timestamp} [${level.toUpperCase()}] ${message}${metaStr ? `: ${metaStr}` : ''}`
}

const commonLogFormat = printf(({ level, message, timestamp, ...meta }) =>
  formatLogMessage(timestamp, level, message, meta),
)

// 控制台格式化
const consoleFormat = combine(
  timestamp({ format: FORMATS.TIME }),
  errors({ stack: true }),
  printf(({ level, message, timestamp, ...meta }) => {
    const formattedMessage = formatLogMessage(timestamp, level, message, meta)
    const colorFn = LOG_CONFIG.chalkColors[level] || (txt => txt)
    return colorFn(formattedMessage)
  }),
)

// 文件格式化
const fileFormat = combine(
  timestamp({ format: FORMATS.DATETIME }),
  errors({ stack: true }),
  commonLogFormat,
)

const messageLogFormat = combine(
  timestamp({ format: FORMATS.DATETIME }),
  errors({ stack: true }),
  format(info => (info.msgType ? info : false))(), // 过滤，只保留有msgType的日志
  commonLogFormat,
)

// 创建文件传输器的工厂函数
const createFileTransport = (filename, options = {}) => {
  const defaultOptions = {
    datePattern: FORMATS.DATE,
    zippedArchive: true,
    maxSize: config.logging.maxSize,
    maxFiles: config.logging.maxFiles,
    format: fileFormat,
  }

  return new transports.DailyRotateFile({
    filename: path.join(config.paths.logs, filename),
    ...defaultOptions,
    ...options,
  })
}

// 创建传输器
const createTransports = () => [
  createFileTransport('app-%DATE%.log'),
  createFileTransport('error-%DATE%.log', { level: 'error' }),
  createFileTransport('message-%DATE%.log', { level: 'info', format: messageLogFormat }),
]

// 日志器工厂函数
const createLoggerInstance = (transportsList, defaultFormat = fileFormat) =>
  createLogger({
    levels: LOG_CONFIG.levels,
    level: config.logging.level,
    format: defaultFormat,
    transports: transportsList,
    exitOnError: false,
  })

// 创建日志器实例
const logger = createLoggerInstance([
  new transports.Console({ level: 'debug', format: consoleFormat }),
  ...createTransports(),
])

const consoleLogger = createLoggerInstance(
  [new transports.Console({ level: config.logging.level, format: consoleFormat })],
  consoleFormat,
)

const fileLogger = createLoggerInstance(createTransports())

// 创建日志方法的工厂函数
const createLogMethods = loggerInstance => {
  const methods = {}
  Object.keys(LOG_CONFIG.levels).forEach(level => {
    methods[level] = (message, context) => loggerInstance[level](message, context)
  })
  return methods
}

// 统一日志接口
export const log = {
  // 标准日志（控制台+文件）
  ...createLogMethods(logger),

  // 仅控制台
  c: createLogMethods(consoleLogger),

  // 仅文件
  f: createLogMethods(fileLogger),
}

// 设置为全局变量
global.log = log

export default log
