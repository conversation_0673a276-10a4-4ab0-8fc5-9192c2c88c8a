import ora from 'ora'
import chalk from 'chalk'
import { deleteFile } from '../utils/fileUtils.js'
import { sendToServer } from '../services/httpClient.js'
import { downloadImage } from '../services/imageDownloader.js'
import {
  formatMessage,
  getContactName,
  saveMessageFile,
  removeWhitespace,
  removeEmojisForAI,
} from '../utils/messageUtils.js'
import { processTextWithAi, processImageWithAi, extractImageWithAi } from '../services/aiService.js'
import { KEYWORD_TYPES, getKeywordStrategy } from './strategies.js'
import DataMatcher from '../utils/dataMatcher.js'
import { serverDataManager } from '../services/dataService.js'
import { processArticle } from '../services/articleService.js'
import { isValidArray, isNonEmptyArray } from '../utils/dataValidators.js'
import { log } from '../utils/logging.js'

// =============== 消息类型配置表 ===============

const MESSAGE_TYPES = {
  1: { name: '文本消息', type: 'TEXT', string: 'text', supported: true },
  3: { name: '图片消息', type: 'IMAGE', string: 'image', supported: true },
  34: { name: '语音消息', type: 'VOICE', string: 'voice', supported: false },
  42: { name: '名片消息', type: 'CARD', string: null, supported: false },
  43: { name: '视频消息', type: 'VIDEO', string: 'video', supported: false },
  47: { name: '表情消息', type: 'EMOJI', string: null, supported: false },
  48: { name: '位置消息', type: 'LOCATION', string: null, supported: false },
  49: { name: '应用消息', type: 'APP', string: 'app', supported: true },
  10000: { name: '系统消息', type: 'SYSTEM', string: 'system', supported: false },
}

const PROCESSING_STATUS = {
  SUCCESS: 'success',
  INVALID_MESSAGE: 'invalid_message',
  CONTACT_NOT_ALLOWED: 'contact_not_allowed',
  NO_KEYWORDS: 'no_keywords',
  EXTRACTION_FAILED: 'extraction_failed',
  PROCESSING_ERROR: 'processing_error',
}

// =============== 简化的结果处理 ===============
const Result = {
  success: (message = '处理成功') => ({ status: 'success', message, success: true }),
  error: (status, message) => ({ status, message, success: false }),
}

// =============== 简化的工具函数 ===============
function isContactAllowed(contacts, contactName, msgType) {
  if (!contactName || !contacts?.contacts) return false
  const contactConfig = contacts.contacts[contactName]
  const typeStr = MESSAGE_TYPES[msgType]?.string
  return contactConfig && typeStr && contactConfig.includes(typeStr)
}

// =============== 关键词处理器 ===============

//关键词匹配处理器
class KeywordProcessor {
  constructor(keywords, priorities) {
    this.keywords = keywords
    this.priorities = priorities
  }

  // 检查消息关键词匹配
  async findMatchingStrategy(msg = {}) {
    if (!msg.content || typeof msg.content !== 'string') {
      return { matched: false, strategy: null }
    }

    if (!this.priorities.length) {
      log.error('未配置关键词类型优先级，无法进行关键词匹配')
      return { matched: false, strategy: null }
    }

    try {
      // 第一阶段：传统关键词匹配
      const keywordResult = this._performTraditionalMatching(msg, this.priorities)
      if (keywordResult.matched) {
        return keywordResult
      }

      // 第二阶段：公司名称匹配（兜底策略）
      const companyResult = await this._performCompanyMatching(msg)
      return companyResult
    } catch (error) {
      log.error('检查关键词失败', { error: error.message, contentLength: msg.content?.length })
      return { matched: false, strategy: null }
    }
  }

  /**
   * 查找匹配的关键词
   */
  _findMatchingKeyword(content, keywords) {
    return keywords.find(keyword => {
      const processedKeyword = removeWhitespace(keyword)
      return content.includes(processedKeyword)
    })
  }

  /**
   * 执行传统关键词匹配
   */
  _performTraditionalMatching(msg, priorities) {
    const priorityOrder = priorities.map(type => KEYWORD_TYPES[type.toUpperCase()])

    for (const type of priorityOrder) {
      const strategy = getKeywordStrategy(type)
      if (!strategy) continue

      const keywords = strategy.getKeywords(this.keywords)
      if (!isNonEmptyArray(keywords)) continue

      const matchedKeyword = this._findMatchingKeyword(msg.content, keywords)
      if (matchedKeyword) {
        log.info(`🎯 匹配到关键词: ${matchedKeyword}, 策略类型: ${strategy.type}`)
        return { matched: true, strategy, matchType: 'keyword' }
      }
    }

    log.debug('未匹配到任何关键词')
    return { matched: false, strategy: null }
  }

  /**
   * 执行公司名称匹配
   */
  async _performCompanyMatching(msg) {
    try {
      const { companyData, cropData } = await serverDataManager.getData()
      if (!companyData || companyData.length === 0 || !cropData || cropData.length === 0) {
        log.debug('公司或作物数据为空，跳过公司匹配')
        return { matched: false, strategy: null }
      }

      const companyMatch = DataMatcher.matchCompany(msg.content, companyData, {
        threshold: 0.6, // 适当降低阈值，提高匹配率
        forceTextMode: true,
      })

      if (companyMatch.matched) {
        log.info(`🎯 匹配到公司名称: ${companyMatch.company.name}`)
        const company = {
          ...companyMatch.company,
          crops: cropData.filter(crop => crop.id === companyMatch.company.cropId),
        }

        return this._handleCompanyMatch(msg, company)
      }

      log.debug('未匹配到任何公司名称')
      return { matched: false, strategy: null }
    } catch (error) {
      log.error(`公司名称匹配失败: ${error.message}`)
      return { matched: false, strategy: null }
    }
  }

  /**
   * 处理公司匹配的分支逻辑
   */
  async _handleCompanyMatch(msg, companyInfo) {
    const companyCrops = companyInfo.crops || []
    const cropCount = Array.isArray(companyCrops) ? companyCrops.length : 0

    // 情况1: 公司拥有多种作物（2种及以上）
    if (cropCount >= 2) {
      log.info(
        `📈 公司"${companyInfo.name}"下有多种作物（${companyCrops.map(c => c.name).join('、')}），已跳过处理`,
      )
      return { matched: false, strategy: null }
    }

    // 情况2: 公司只有单一作物
    if (cropCount === 1) {
      const cropName = companyCrops[0].name
      const enhancedContent = `${msg.content}_${cropName}`

      log.info(`🔄 公司只有单一作物"${cropName}"，拼接内容: ${enhancedContent}`)

      // 使用拼接后的内容重新进行关键词匹配
      const enhancedMsg = { ...msg, content: enhancedContent }
      const keywordResult = this._performTraditionalMatching(enhancedMsg, this.priorities)

      if (keywordResult.matched) {
        // 返回增强后的策略结果，但不修改原始消息对象
        return {
          ...keywordResult,
          enhancedContent: enhancedContent,
          originalContent: msg.content,
        }
      }

      log.warn(`⚠️  公司"${companyInfo.name}"的作物"${cropName}"未能匹配到相应的处理策略`)
      return { matched: false, strategy: null }
    }

    // 情况3: 公司没有作物信息
    log.warn(`⚠️  公司"${companyInfo.name}"没有作物信息，无法进行进一步处理`)
    return { matched: false, strategy: null }
  }
}

// =============== 简化的资源清理 ===============
async function cleanupFiles(files) {
  for (const file of files) {
    try {
      await deleteFile(file)
    } catch (error) {
      // 忽略清理失败，不影响主流程
    }
  }
}

// =============== 简化的图片处理 ===============
async function processImage(agent, msg, spinner) {
  if (!msg || msg.type !== 3) return { success: false, reason: '无效的图片消息' }

  let imagePath = null
  try {
    // 下载图片
    spinner.start(chalk.cyan('正在下载图片...'))
    imagePath = (await saveMessageFile(agent, msg)) || (await downloadImage(agent, msg))
    if (!imagePath) {
      spinner.fail(chalk.red('图片下载失败'))
      return { success: false, reason: '图片下载失败' }
    }
    spinner.succeed(chalk.green('图片下载完成'))

    // 提取文字
    spinner.start(chalk.cyan('正在识别图片内容...'))
    const { text, fileId } = await extractImageWithAi(imagePath)

    if (!text) {
      spinner.fail(chalk.yellow('图片中未识别到文字内容'))
      return { success: false, imagePath, reason: '图片未提取到有效文字内容' }
    }

    spinner.succeed(chalk.green('图片内容识别完成'))
    return { success: true, imagePath, text, fileId }
  } catch (error) {
    spinner.fail(chalk.red('图片处理失败'))
    return { success: false, imagePath, reason: error.message }
  }
}

// =============== 简化的AI处理 ===============
async function processWithAI(processFn, strategy, spinner, msg) {
  const businessType = strategy.config?.businessType || strategy.type
  const promptType = strategy.config?.promptType || strategy.type
  const messages = strategy.getMessages()

  spinner.start(chalk.cyan(`${messages.start}`))

  try {
    const structuredInfo = await processFn(msg, promptType)

    if (structuredInfo && isValidArray(structuredInfo) && structuredInfo.length > 0) {
      spinner.succeed(chalk.green(`${messages.success}`))
      return { success: true, structuredInfo, businessType, promptType }
    }

    spinner.fail(chalk.red(`${messages.error}`))
    return { success: false, structuredInfo: null, businessType }
  } catch (error) {
    spinner.fail(chalk.red(`${messages.error}: ${error.message}`))
    return { success: false, structuredInfo: null, businessType }
  }
}

// =============== 简化的消息转发 ===============
async function forwardMessage(agent, msg, structuredInfo, businessType, promptType) {
  try {
    const formattedMsg = await formatMessage(agent, msg)

    // 清理结构化信息
    if (structuredInfo && isValidArray(structuredInfo)) {
      structuredInfo = structuredInfo.map(item => {
        if (!item || typeof item !== 'object') return item
        const cleanedItem = { ...item }
        Object.keys(cleanedItem).forEach(key => {
          if (typeof cleanedItem[key] === 'string') {
            cleanedItem[key] = removeEmojisForAI(cleanedItem[key])
          }
        })
        return cleanedItem
      })

      // 简单的业务规则验证
      const cropGroups = structuredInfo.reduce((groups, item) => {
        if (item.crop) groups[item.crop] = (groups[item.crop] || []).concat(item)
        return groups
      }, {})

      const exceedingCrop = ['小麦', '玉米'].find(crop => cropGroups[crop]?.length > 2)
      if (exceedingCrop && promptType === KEYWORD_TYPES.NORMAL) {
        log.warn(
          `⚠️  AI提取到${exceedingCrop}数据${cropGroups[exceedingCrop].length}条，超过限制(2条)，已跳过处理`,
        )
        return false
      }
    }

    formattedMsg.structuredInfo = structuredInfo ? JSON.stringify(structuredInfo) : null
    formattedMsg.dataType = businessType

    await sendToServer(formattedMsg)
    log.success(`✓ ${MESSAGE_TYPES[msg.type]?.name || '消息'}处理完成并已转发 (${businessType})`)
    return true
  } catch (error) {
    log.error(`❌ ${MESSAGE_TYPES[msg.type]?.name || '消息'}处理失败: ${error?.message}`)
    return false
  }
}

// =============== 简化的消息处理器 ===============
class MessageHandler {
  constructor(agent, configs) {
    this.agent = agent
    this.configs = configs
    this.spinner = ora({ color: 'cyan', text: chalk.cyan('处理中...') })
    this.keywordProcessor = new KeywordProcessor(
      configs.keywords,
      configs.priorities?.keywordTypePriorities || [],
    )
    this.filesToClean = []
  }

  /**
   * 处理消息的主入口
   */
  async processMessage(msg) {
    try {
      if (!msg || typeof msg.type === 'undefined') {
        return Result.error('invalid_message', '无效消息对象')
      }

      // 联系人验证
      const contactName = getContactName(this.agent, msg.sender)
      if (!contactName || !isContactAllowed(this.configs.contacts, contactName, msg.type)) {
        return Result.error(
          'contact_not_allowed',
          contactName ? '联系人不在允许列表中' : '无法获取联系人信息',
        )
      }

      // 检查消息类型是否支持
      if (!MESSAGE_TYPES[msg.type]?.supported) {
        return Result.error(
          'invalid_message',
          `不支持的消息类型: ${MESSAGE_TYPES[msg.type]?.name || '未知类型'}`,
        )
      }

      // 根据消息类型处理
      const handlers = {
        1: () => this._handleTextMessage(msg),
        3: () => this._handleImageMessage(msg),
        49: () => this._handleAppMessage(msg),
      }

      return await handlers[msg.type]()
    } catch (error) {
      log.error('消息处理异常', { error: error.message, stack: error.stack })
      return Result.error('processing_error', `消息处理失败: ${error.message}`)
    } finally {
      // 清理文件
      await cleanupFiles(this.filesToClean)
      this.filesToClean = []
    }
  }

  /**
   * 通用的AI处理和转发方法
   */
  async _processWithAI(msg, processFn) {
    const matchResult = await this.keywordProcessor.findMatchingStrategy(msg)
    if (!matchResult.matched) {
      return Result.error('no_keywords', '消息不包含关键词')
    }
    const messageType = MESSAGE_TYPES[msg.type]?.name || '未知类型'

    // 如果有增强内容，使用增强内容进行AI处理
    const processMsg = matchResult.enhancedContent
      ? { ...msg, content: matchResult.enhancedContent }
      : msg

    const aiResult = await processWithAI(processFn, matchResult.strategy, this.spinner, processMsg)
    if (!aiResult.success) {
      return Result.error('processing_error', `${messageType}AI处理失败`)
    }

    const forwardSuccess = await forwardMessage(
      this.agent,
      msg,
      aiResult.structuredInfo,
      aiResult.businessType,
      aiResult.promptType,
    )

    return forwardSuccess
      ? Result.success(`${aiResult.businessType}${messageType}处理成功`)
      : Result.error('processing_error', `${messageType}转发失败`)
  }

  /**
   * 处理文本消息
   */
  async _handleTextMessage(msg) {
    return this._processWithAI(
      msg,
      async (msg, promptType) => await processTextWithAi(msg.content, promptType),
    )
  }

  /**
   * 处理图片消息
   */
  async _handleImageMessage(msg) {
    // 下载并提取图片文字
    const extractResult = await processImage(this.agent, msg, this.spinner)
    if (!extractResult.success) {
      return Result.error('extraction_failed', extractResult.reason || '图片处理失败')
    }

    msg.content = extractResult.text
    msg.fileId = extractResult.fileId
    msg.imagePath = extractResult.imagePath

    return this._processWithAI(
      msg,
      async (msg, promptType) => await processImageWithAi(msg, promptType),
    )
  }

  /**
   * 处理应用消息
   */
  async _handleAppMessage(msg) {
    // 处理文章内容
    const articleResult = await processArticle(msg)
    if (!articleResult.success) {
      return Result.error('processing_error', articleResult.error)
    }

    const { formattedContent, processingStats } = articleResult.data
    msg.content = formattedContent

    // 简单的统计日志
    if (processingStats?.fallbackMode) {
      log.warn(`文章处理使用无图片模式: 图片处理${processingStats.failedImages || 0}张失败`)
    } else if (processingStats?.totalImages > 0) {
      log.info(`文章处理完成: 包含${processingStats.successImages || 0}张图片内容`)
    }

    return this._processWithAI(
      msg,
      async (msg, promptType) => await processTextWithAi(msg.content, promptType),
    )
  }
}

// =============== 主处理函数 ===============

/**
 * 处理接收到的消息
 * @param {Object} msg - 消息对象，包含消息的各种属性如内容、类型等
 * @param {Object} agent - 消息代理对象，用于处理和发送消息
 * @param {Object} configs - 配置对象，包含关键词、联系人等配置信息
 * @returns {Promise<Object>} 处理结果，包含状态和消息
 */
async function handleMessage(msg, agent, configs) {
  if (!msg) return Result.error('invalid_message', '无效消息对象')
  if (!agent) return Result.error('invalid_agent', 'agent未初始化')
  if (!configs) return Result.error('invalid_configs', '无效的配置对象')

  // 清理消息内容中的表情符号
  if (msg.content && typeof msg.content === 'string') {
    msg.originalContent = msg.content
    msg.content = removeWhitespace(removeEmojisForAI(msg.content))
  }

  const handler = new MessageHandler(agent, configs)
  return await handler.processMessage(msg)
}

export default handleMessage
