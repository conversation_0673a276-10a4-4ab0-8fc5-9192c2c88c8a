import axios from 'axios'
import axiosRetry from 'axios-retry'
import fs from 'fs'
import path from 'path'
import ora from 'ora'
import chalk from 'chalk'
import crypto from 'crypto'
import { CookieJar } from 'tough-cookie'
import FileCookieStore from 'tough-cookie-file-store'
import FormData from 'form-data'
import config from '../config/index.js'
import { log } from '../utils/logging.js'

const logger = {
  info: (msg, data) => log.info(msg, data),
  success: (msg, data) => log.success(msg, data),
  warn: (msg, data) => log.warn(msg, data),
  error: (msg, data) => log.error(msg, data),
  debug: (msg, data) => log.c.debug(msg, data),
}

const CONFIG = {
  COOKIE_FILE_PATH: config.paths.cookies,
  RETRY_ATTEMPTS: 3,
  AUTH_COOKIE_KEY: 'qwc_session',
  CACHE_DURATION: 60000,
  ERROR_CODES: { AUTH_REQUIRED: -1, UNAUTHORIZED: 401, FORBIDDEN: 403 },
  // 白名单：不需要认证的请求路径
  WHITELIST_PATHS: [config.server.auth.loginUrl].filter(Boolean),
}

const validateFile = value => {
  if (typeof value === 'string' && fs.existsSync(value)) {
    return {
      path: value,
      filename: path.basename(value),
      stream: fs.createReadStream(value),
    }
  }
  if (value instanceof fs.ReadStream && value.path && fs.existsSync(value.path)) {
    return {
      path: value.path,
      filename: path.basename(value.path),
      stream: value,
    }
  }
  return null
}

const jar = new CookieJar(new FileCookieStore(CONFIG.COOKIE_FILE_PATH))

const httpClient = axios.create({
  baseURL: config.server.url,
  timeout: config.server.timeout,
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true,
  jar,
})

axiosRetry(httpClient, {
  retries: CONFIG.RETRY_ATTEMPTS,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: error =>
    axiosRetry.isNetworkOrIdempotentRequestError(error) ||
    ['ECONNABORTED', 'ETIMEDOUT'].includes(error.code) ||
    /timeout|Network Error/i.test(error.message),
  onRetry: (retryCount, error, config) => {
    const operation = `${config.method?.toUpperCase() || 'GET'} ${config.url}`
    logger.warn(`网络重试 ${retryCount}/${CONFIG.RETRY_ATTEMPTS}: ${operation}`)
  },
})

// 简化的认证管理器 - 只处理内部状态，不暴露队列细节
class AuthManager {
  constructor() {
    this.state = { isAuthenticated: false, loginInProgress: false, loginPromise: null }
    this.sessionCache = { lastCheck: 0, isValid: false }
    this.spinner = ora({ color: 'cyan', text: chalk.cyan('登录中...') })
    // 等待登录完成的Promise列表
    this.waitingPromises = []
  }

  async login() {
    if (this.state.loginPromise) {
      return this.state.loginPromise
    }

    try {
      this.state.loginInProgress = true
      this.state.loginPromise = this._performLogin()
      const result = await this.state.loginPromise

      // 登录完成后，resolve所有等待的Promise
      this.resolveWaitingPromises(result)

      return result
    } catch (error) {
      // 登录异常时，reject所有等待的Promise
      this.rejectWaitingPromises(error)
      throw error
    } finally {
      this.state.loginInProgress = false
      this.state.loginPromise = null
    }
  }

  // 添加等待登录完成的Promise
  addWaitingPromise() {
    return new Promise((resolve, reject) => {
      this.waitingPromises.push({ resolve, reject })
    })
  }

  // 解决所有等待的Promise
  resolveWaitingPromises(result) {
    const promises = [...this.waitingPromises]
    this.waitingPromises = []

    promises.forEach(({ resolve }) => resolve(result))
  }

  // 拒绝所有等待的Promise
  rejectWaitingPromises(error) {
    const promises = [...this.waitingPromises]
    this.waitingPromises = []

    promises.forEach(({ reject }) => reject(error))
  }

  async _performLogin() {
    try {
      const { username, password, loginUrl } = config.server.auth

      if (!username || !password || !loginUrl) {
        throw new Error('登录失败：缺少登录信息，请检查环境变量配置')
      }

      const hashedPassword = crypto.createHash('md5').update(password).digest('hex')
      this.spinner.start(chalk.cyan('正在连接服务器...'))

      const { data } = await httpClient.post(loginUrl, {
        mobile: username,
        password: hashedPassword,
      })

      if (data?.code === 1) {
        this.state.isAuthenticated = true
        this.sessionCache.lastCheck = 0
        this.spinner.succeed(chalk.green('服务器连接成功'))
        logger.success('服务器认证成功')
        jar.removeAllExpiredCookiesSync?.()
        return true
      }

      const errorMsg = data?.message || '认证失败'
      this.spinner.fail(chalk.red(`${errorMsg}`))
      logger.error(`服务器登录失败: ${errorMsg}`)
      this.state.isAuthenticated = false
      return false
    } catch (error) {
      this.spinner.fail(chalk.red('服务器连接失败'))
      logger.error(`网络连接异常: ${error.message}`)
      this.state.isAuthenticated = false
      return false
    }
  }

  hasValidSession(url) {
    try {
      const now = Date.now()
      if (now - this.sessionCache.lastCheck < CONFIG.CACHE_DURATION) {
        return this.sessionCache.isValid
      }

      const isValid = jar
        .getCookiesSync(url)
        .some(c => c.key === CONFIG.AUTH_COOKIE_KEY && (!c.expires || c.expires > Date.now()))

      this.sessionCache = { lastCheck: now, isValid }
      return isValid
    } catch (error) {
      logger.warn(`检查会话有效性失败: ${error.message}`)
      this.sessionCache = { lastCheck: Date.now(), isValid: false }
      return false
    }
  }

  clearSessionCache() {
    this.sessionCache = { lastCheck: 0, isValid: false }
  }

  // 检查请求是否在白名单中
  isWhitelistedRequest(url) {
    if (!url) return false

    const urlPath = new URL(url, 'http://localhost').pathname
    if (urlPath === config.server.auth.loginUrl) return true

    return CONFIG.WHITELIST_PATHS.some(path => {
      if (!path || typeof path !== 'string') return false
      if (path.endsWith('/')) {
        return urlPath.startsWith(path)
      }
      return urlPath === path
    })
  }

  async handleAuthRetry(originalConfig, context = 'unknown') {
    if (originalConfig._retry) {
      throw new Error('认证失败，请检查登录配置')
    }

    logger.info(`检测到认证错误，正在重新认证... (来源: ${context})`)
    originalConfig._retry = true

    const loginSuccess = await this.login()
    if (loginSuccess) {
      this.clearSessionCache()
      return httpClient(originalConfig)
    }

    logger.error('重新认证失败，请检查服务器配置')
    throw new Error('认证失败，请检查登录配置')
  }
}

const authManager = new AuthManager()

// 简化的请求拦截器 - 透明处理认证
const setupInterceptors = () => {
  httpClient.interceptors.request.use(
    async cfg => {
      if (!cfg?.url) throw new Error('请求配置无效')

      const baseURL = cfg.baseURL || config.server.url || 'http://localhost'
      const fullUrl = cfg.url.startsWith('http') ? cfg.url : new URL(cfg.url, baseURL).href

      // 白名单请求直接通过
      if (authManager.isWhitelistedRequest(fullUrl)) {
        logger.debug(`白名单请求，直接通过: ${cfg.url}`)
      } else {
        // 非白名单请求需要认证
        if (!authManager.hasValidSession(baseURL)) {
          if (authManager.state.loginInProgress) {
            // 如果正在登录，等待登录完成
            logger.info(`等待登录完成: ${cfg.url}`)
            await authManager.addWaitingPromise()
          } else {
            // 开始登录流程
            logger.info('会话过期，正在重新认证...')
            const loginSuccess = await authManager.login()
            if (!loginSuccess) {
              throw new Error('认证失败，请检查服务器配置')
            }
          }
        }
      }

      // 为所有请求添加Cookie
      try {
        const cookieString = await jar.getCookieString(baseURL)
        if (cookieString) {
          cfg.headers = cfg.headers || {}
          cfg.headers.Cookie = cookieString
        }
      } catch (error) {
        logger.warn(`获取Cookie失败: ${error.message}`)
      }

      return cfg
    },
    error => {
      logger.error(`请求拦截器错误: ${error.message}`)
      return Promise.reject(error)
    },
  )

  httpClient.interceptors.response.use(
    async response => {
      if (!response?.config) {
        logger.warn('响应对象无效')
        return response
      }

      const baseURL = response.config.baseURL || config.server.url

      try {
        const setCookies = response.headers?.['set-cookie'] || []
        if (setCookies.length > 0) {
          await Promise.all(
            setCookies.map(
              cookie =>
                new Promise((resolve, reject) => {
                  jar.setCookie(cookie, baseURL, (err, cookie) => {
                    if (err) reject(err)
                    else resolve(cookie)
                  })
                }),
            ),
          )
        }
      } catch (error) {
        logger.warn(`存储Cookie失败: ${error.message}`)
      }

      if (response.data?.code === CONFIG.ERROR_CODES.AUTH_REQUIRED) {
        return authManager.handleAuthRetry(response.config, 'response-interceptor')
      }

      return response
    },
    async error => {
      const { status, config: origCfg, response } = error
      const isAuthError =
        (status &&
          [CONFIG.ERROR_CODES.UNAUTHORIZED, CONFIG.ERROR_CODES.FORBIDDEN].includes(status)) ||
        (response?.data?.code === CONFIG.ERROR_CODES.AUTH_REQUIRED && origCfg)

      if (isAuthError && origCfg) {
        return authManager.handleAuthRetry(origCfg, 'error-interceptor')
      }

      return Promise.reject(error)
    },
  )
}

setupInterceptors()

const closeStreams = streams => {
  streams.forEach(stream => {
    try {
      if (stream && typeof stream.destroy === 'function') {
        stream.destroy()
      }
    } catch (error) {
      logger.debug(`关闭文件流失败: ${error.message}`)
    }
  })
}

async function sendToServer(payload) {
  if (!payload) throw new Error('无效的消息载荷')

  const messageUrl = config.api.server.message
  if (!messageUrl) throw new Error('未配置消息发送URL，请检查环境变量')

  let form = null
  const openStreams = []

  try {
    form = new FormData()
    for (const [key, value] of Object.entries(payload)) {
      if (value === undefined || value === null) continue

      if (key === 'file') {
        const fileInfo = validateFile(value)
        if (fileInfo) {
          form.append(key, fileInfo.stream, fileInfo.filename)
          openStreams.push(fileInfo.stream)
        } else {
          log.c.warn(`无效的文件值类型: ${typeof value}`)
        }
      } else {
        form.append(key, value)
      }
    }

    log.f.info('发送消息', { msgType: 'RAW', payload })
    const { data } = await httpClient.post(messageUrl, form, {
      headers: form.getHeaders(),
    })

    logger.success('发送成功', { msgType: 'success', messages: data.message, results: data.data })
    return data
  } catch (error) {
    const { status, data, response } = error
    const errorInfo = {
      msgType: 'failure',
      status: (status === 200 ? data?.code : response?.status) || error.code || status,
      error: (status === 200 ? data?.message : response?.data?.message) || error.message,
    }

    logger.error('发送失败', errorInfo)
    throw error
  } finally {
    closeStreams(openStreams)
    if (form?._streams) closeStreams(form._streams)
  }
}

export { httpClient, sendToServer, authManager }
