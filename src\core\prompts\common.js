/**
 * 通用提示词工具模块
 * 包含所有提示词类型共享的基础结构和工具函数
 */

import { FORMATS } from '../../utils/dateUtils.js'

// 通用配置常量
export const CONFIG = {
  DEFAULT_UNIT: '元/斤',
  DECIMAL_PLACES: 4,
  DATE_FORMAT: FORMATS.DATE, // 使用统一的日期格式
  STANDARD_MOISTURE: 14, // 标准水分含量
  WET_GRAIN_MOISTURE: 30, // 潮粮水分含量
}

// 优先级权重配置 - 与 prompt 保持一致
// ==================== 优先级权重配置（已整合） ====================
export const PRIORITY_CONFIGS = {
  NORMAL: [
    { title: '水分条件', weights: { '≤14%': 5, '>14%': 0 } },
    { title: '厂区类型', weights: { 老厂: 3, 新厂: 0 } },
    { title: '价格类型', weights: { 基础价: 2, 最高价: 0 } },
    { title: '作物品质', weights: { 白麦普麦: 4, 白麦优麦: 3, 红麦普麦: 2, 红麦优麦: 1, 新麦: 0 } },
    { title: '票据状态', weights: { 无票: 1, 带票: 0 } },
  ],
  UREA: [
    { title: '颗粒类型', weights: { 中颗粒: 3, 小颗粒: 2, 大颗粒: 1 } },
    { title: '包装类型', weights: { 小包装: 2, 吨包: 1, 散装: 0 } },
    { title: '质量等级', weights: { 高品质: 2, 普通: 1, 未标明: 0 } },
  ],
  IMPORTED: [
    { title: '港口重要性', weights: { 美湾: 3, 美西: 3, 巴西: 2, 阿根廷: 2, 黑海: 1 } },
    { title: '数据完整性', weights: { 完整数据: 2, 部分数据: 1, 缺失数据: 0 } },
    { title: '时效性', weights: { 当月: 3, 次月: 2, 远期: 1 } },
  ],
  DETAILS_ONLY: [
    { title: '信息明确性', weights: { 明确具体: 3, 基本明确: 2, 模糊不清: 1, 无法确定: 0 } },
    { title: '企业名称完整性', weights: { 全称: 2, 简称: 1, 模糊: 0 } },
    { title: '作物匹配度', weights: { 完全匹配: 2, 部分匹配: 1, 无法匹配: 0 } },
  ],
  ARTICLE: [
    { title: '信息来源', weights: { 直接报价: 4, 企业官方: 3, 媒体报道: 2, 转述信息: 1 } },
    { title: '时效性', weights: { 当日价格: 3, 近期价格: 2, 历史数据: 1, 预测信息: 0 } },
    { title: '数据可靠性', weights: { 具体数据: 3, 官方发布: 2, 估算预测: 1, 模糊描述: 0 } },
  ],
  TRANSPORT: [
    { title: '数据完整性', weights: { 完整数据: 3, 部分数据: 1, 空数据: 0 } },
    { title: '数据类型', weights: { 企业车辆: 2, 港口运输: 2, 其他: 1 } },
    { title: '数值有效性', weights: { 有效数值: 2, 零值: 1, 空值: 0 } },
  ],
  IMAGE: [
    { title: '清晰度', weights: { 清晰完整: 3, 基本清晰: 2, 部分模糊: 1, 严重模糊: 0 } },
    { title: '位置', weights: { 表格正文: 3, 标题区域: 2, 边缘信息: 1, 背景文字: 0 } },
    { title: '完整性', weights: { 完整记录: 3, 部分信息: 2, 片段数据: 1, 不完整: 0 } },
  ],
}

// 单位转换映射
export const UNIT_CONVERSION = {
  '元/公斤': 0.5,
  '元/kg': 0.5,
  '元/千克': 0.5,
  '元/斤': 1,
  '元/500g': 1,
  '元/吨': 1 / 2000, // 1吨 = 2000斤
  '元/t': 1 / 2000,
  '元/T': 1 / 2000,
  // 新增单位
  '元/KG': 0.5,
  '元/Kg': 0.5,
  '元/MT': 1 / 2000,
  '元/mt': 1 / 2000,
  '元/磅': 2.20462,
  '元/lb': 2.20462,
  '分/斤': 0.01,
  '角/斤': 0.1,
}

// ==================== 通用格式化工具 ====================

/**
 * 通用函数：格式化优先级权重配置
 * @param {Array<Object>} sections - 包含标题和权重对象的配置数组
 * @returns {string} 格式化后的权重信息字符串
 */
function formatWeights(sections = []) {
  return sections
    .map(section => {
      const weightEntries = Object.entries(section.weights)
        .map(([key, value]) => `${key}(${value}分)`)
        .join(' > ')
      return `${section.title}：${weightEntries}`
    })
    .join('\n')
}

/**
 * 格式化提示词模板
 * @param {string} promptContent - 提示词模板内容
 * @param {string} currentDate - 当前日期 YYYY-MM-DD
 * @param {Array} cropsInfo - 作物信息数组
 * @param {Object} options - 可选配置
 * @returns {string} 格式化后的提示词
 */
export function formatPromptTemplate(promptContent, currentDate, cropsInfo = [], options = {}) {
  const config = { ...CONFIG, ...options }
  let formattedContent = promptContent || ''

  if (!formattedContent) {
    console.warn('警告: 提示词内容为空')
    return '请分析以下内容，提取关键信息，并输出JSON格式的结构化数据。'
  }

  // 定义所有可能的替换项
  const replacements = {
    '${cropsInfo}': JSON.stringify(cropsInfo, null, 2).replace(/`/g, '`'),
    '${config.DECIMAL_PLACES}': config.DECIMAL_PLACES,
    '${config.DEFAULT_UNIT}': config.DEFAULT_UNIT,
    '${config.DATE_FORMAT}': config.DATE_FORMAT,
    '${UNIT_CONVERSION}': Object.entries(UNIT_CONVERSION)
      .map(([unit, rate]) => `"${unit}" → "${config.DEFAULT_UNIT}" (×${rate})`)
      .join('\n'),
    '${currentDate}': currentDate || '',
    '${config.STANDARD_MOISTURE}': config.STANDARD_MOISTURE,
    '${config.WET_GRAIN_MOISTURE}': config.WET_GRAIN_MOISTURE,
    '${PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.NORMAL),
    '${UREA_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.UREA),
    '${IMPORTED_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.IMPORTED),
    '${DETAILS_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.DETAILS_ONLY),
    '${ARTICLE_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.ARTICLE),
    '${TRANSPORT_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.TRANSPORT),
    '${IMAGE_PRIORITY_WEIGHTS}': formatWeights(PRIORITY_CONFIGS.IMAGE),
  }

  // 使用正则表达式一次性替换所有占位符
  // 这比链式调用 .replace() 更高效
  formattedContent = formattedContent.replace(
    /\${[a-zA-Z0-9_.]*}/g,
    match => replacements[match] || match,
  )

  return formattedContent
}

/**
 * 构建图片处理扩展提示词
 * @param {string} imageExtensionContent - 图片扩展提示词内容
 * @param {string} basePrompt - 基础提示词
 * @returns {string} 完整的提示词
 */
export function addImageExtension(imageExtensionContent, basePrompt) {
  return `${basePrompt}\n${imageExtensionContent}`
}

// 为了向后兼容，保留原函数名的别名
export const buildBasePrompt = formatPromptTemplate
export const buildImageExtension = addImageExtension
