import 'dotenv/config'
import { disableExperimentalWarnings } from '../utils/nodeWarnings.js'
import { FileUtils } from '../utils/fileUtils.js'
import { ErrorHandler } from '../utils/errorHandler.js'
import { ApplicationManager } from './applicationManager.js'
import config, { validateEnv } from '../config/index.js'
import { loadAllConfigs } from '../core/configLoader.js'
import { log } from '../utils/logging.js'

async function bootstrap() {
  disableExperimentalWarnings()
  validateEnv()

  if (!FileUtils.ensureDirsSync([config.paths.logs, config.paths.files])) {
    log.error('创建必要的目录失败，应用程序无法继续运行')
    process.exit(1)
  }

  ErrorHandler.register()

  const configs = await loadAllConfigs()
  const app = new ApplicationManager(configs)
  await app.start()
}

bootstrap().catch(error => {
  log.error(`应用启动失败: ${error.message}`)
  process.exit(1)
})
