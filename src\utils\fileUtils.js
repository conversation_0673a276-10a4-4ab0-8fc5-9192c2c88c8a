import fs from 'fs'
import fsPromises from 'fs/promises'
import path from 'path'
import { safeAsync, safeFileOperation } from './errorHandler.js'
import { isNonEmptyArray } from './dataValidators.js'
import { log } from './logging.js'

/**
 * 文件操作工具类
 */
export class FileUtils {
  /**
   * 安全删除文件
   * @param {string} filePath - 要删除的文件路径
   * @returns {Promise<boolean>} - 是否删除成功
   */
  static async deleteFile(filePath) {
    if (!filePath) return false

    return safeFileOperation(
      async () => {
        // 先检查文件是否存在，避免ENOENT错误
        try {
          await fsPromises.access(filePath, fs.constants.F_OK)
        } catch (error) {
          if (error.code === 'ENOENT') {
            log.c.debug(`文件不存在，跳过删除: ${filePath}`)
            return true // 文件不存在视为删除成功
          }
          throw error // 其他错误继续抛出
        }

        // 删除文件
        await fsPromises.unlink(filePath)
        log.c.info(`已删除本地文件: ${filePath}`)
        return true
      },
      filePath,
      false,
    )
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath - 要检查的文件路径
   * @returns {Promise<boolean>} - 文件是否存在
   */
  static async fileExists(filePath) {
    if (!filePath) return false

    return safeFileOperation(
      async () => {
        await fsPromises.access(filePath, fs.constants.F_OK)
        return true
      },
      filePath,
      false,
    )
  }

  /**
   * 同步检查文件是否存在
   * @param {string} filePath - 要检查的文件路径
   * @returns {boolean} - 文件是否存在
   */
  static fileExistsSync(filePath) {
    if (!filePath) {
      return false
    }

    try {
      fs.accessSync(filePath, fs.constants.F_OK)
      return true
    } catch {
      return false
    }
  }

  /**
   * 确保目录存在，如果不存在则创建
   * @param {string} dirPath - 目录路径
   * @param {Object} options - 创建选项
   * @returns {Promise<boolean>} - 是否成功
   */
  static async ensureDir(dirPath, options = { recursive: true }) {
    if (!dirPath) return false

    return safeFileOperation(
      async () => {
        try {
          await fsPromises.access(dirPath, fs.constants.F_OK)
          return true
        } catch {
          // 目录不存在，创建它
          await fsPromises.mkdir(dirPath, options)
          log.c.info(`已创建目录: ${dirPath}`)
          return true
        }
      },
      dirPath,
      false,
    )
  }

  /**
   * 同步确保目录存在，如果不存在则创建
   * @param {string} dirPath - 目录路径
   * @param {Object} options - 创建选项
   * @returns {boolean} - 是否成功
   */
  static ensureDirSync(dirPath, options = { recursive: true }) {
    if (!dirPath) {
      return false
    }

    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, options)
        log.c.info(`已创建目录: ${dirPath}`)
      }
      return true
    } catch (error) {
      log.c.error(`创建目录失败: ${dirPath}`, { error: error.message })
      return false
    }
  }

  /**
   * 确保多个目录存在
   * @param {Array<string>} dirs - 目录路径数组
   * @returns {Promise<boolean>} - 是否全部成功
   */
  static async ensureDirs(dirs) {
    if (!isNonEmptyArray(dirs)) {
      return false
    }

    try {
      const results = await Promise.all(dirs.map(dir => FileUtils.ensureDir(dir)))
      return results.every(result => result === true)
    } catch (error) {
      log.c.error('创建多个目录失败', { error: error.message })
      return false
    }
  }

  /**
   * 同步确保多个目录存在
   * @param {Array<string>} dirs - 目录路径数组
   * @returns {boolean} - 是否全部成功
   */
  static ensureDirsSync(dirs) {
    if (!isNonEmptyArray(dirs)) {
      return false
    }

    try {
      return dirs.every(dir => FileUtils.ensureDirSync(dir))
    } catch (error) {
      log.c.error('创建多个目录失败', { error: error.message })
      return false
    }
  }

  /**
   * 复制文件
   * @param {string} src - 源文件路径
   * @param {string} dest - 目标文件路径
   * @returns {Promise<boolean>} - 是否成功
   */
  static async copyFile(src, dest) {
    if (!src || !dest) return false

    return safeFileOperation(
      async () => {
        // 确保源文件存在
        await fsPromises.access(src, fs.constants.R_OK)

        // 确保目标目录存在
        const destDir = path.dirname(dest)
        await FileUtils.ensureDir(destDir)

        // 复制文件
        await fsPromises.copyFile(src, dest)
        log.c.info(`已复制文件: ${src} -> ${dest}`)
        return true
      },
      `${src} -> ${dest}`,
      false,
    )
  }

  /**
   * 同步复制文件
   * @param {string} src - 源文件路径
   * @param {string} dest - 目标文件路径
   * @returns {boolean} - 是否成功
   */
  static copyFileSync(src, dest) {
    if (!src || !dest) {
      return false
    }

    try {
      // 确保源文件存在
      if (!fs.existsSync(src)) {
        log.c.error(`源文件不存在: ${src}`)
        return false
      }

      // 确保目标目录存在
      const destDir = path.dirname(dest)
      FileUtils.ensureDirSync(destDir)

      // 复制文件
      fs.copyFileSync(src, dest)
      log.c.info(`已复制文件: ${src} -> ${dest}`)
      return true
    } catch (error) {
      log.c.error(`复制文件失败: ${src} -> ${dest}`, { error: error.message })
      return false
    }
  }

  /**
   * 读取文件内容
   * @param {string} filePath - 文件路径
   * @param {Object} options - 读取选项
   * @returns {Promise<string|Buffer|null>} - 文件内容
   */
  static async readFile(filePath, options = { encoding: 'utf8' }) {
    if (!filePath) return null

    return safeFileOperation(
      async () => {
        // 检查文件是否可读
        await fsPromises.access(filePath, fs.constants.R_OK)
        return await fsPromises.readFile(filePath, options)
      },
      filePath,
      null,
    )
  }

  /**
   * 同步读取文件内容
   * @param {string} filePath - 文件路径
   * @param {Object} options - 读取选项
   * @returns {string|Buffer|null} - 文件内容
   */
  static readFileSync(filePath, options = { encoding: 'utf8' }) {
    if (!filePath) {
      return null
    }

    try {
      if (!fs.existsSync(filePath)) {
        log.c.error(`文件不存在: ${filePath}`)
        return null
      }

      return fs.readFileSync(filePath, options)
    } catch (error) {
      log.c.error(`读取文件失败: ${filePath}`, { error: error.message })
      return null
    }
  }

  /**
   * 写入文件内容
   * @param {string} filePath - 文件路径
   * @param {string|Buffer} data - 要写入的数据
   * @param {Object} options - 写入选项
   * @returns {Promise<boolean>} - 是否成功
   */
  static async writeFile(filePath, data, options = { encoding: 'utf8' }) {
    if (!filePath) return false

    return safeFileOperation(
      async () => {
        // 确保目标目录存在
        const destDir = path.dirname(filePath)
        await FileUtils.ensureDir(destDir)

        // 写入文件
        await fsPromises.writeFile(filePath, data, options)
        log.c.info(`已写入文件: ${filePath}`)
        return true
      },
      filePath,
      false,
    )
  }

  /**
   * 同步写入文件内容
   * @param {string} filePath - 文件路径
   * @param {string|Buffer} data - 要写入的数据
   * @param {Object} options - 写入选项
   * @returns {boolean} - 是否成功
   */
  static writeFileSync(filePath, data, options = { encoding: 'utf8' }) {
    if (!filePath) {
      return false
    }

    try {
      // 确保目标目录存在
      const destDir = path.dirname(filePath)
      FileUtils.ensureDirSync(destDir)

      // 写入文件
      fs.writeFileSync(filePath, data, options)
      log.c.info(`已写入文件: ${filePath}`)
      return true
    } catch (error) {
      log.c.error(`写入文件失败: ${filePath}`, { error: error.message })
      return false
    }
  }

  /**
   * 检查文件是否可读
   * @param {string} filePath - 文件路径
   * @returns {Promise<boolean>} - 文件是否可读
   */
  static async isReadable(filePath) {
    if (!filePath) {
      return false
    }

    try {
      await fsPromises.access(filePath, fs.constants.R_OK)
      return true
    } catch {
      return false
    }
  }

  /**
   * 同步检查文件是否可读
   * @param {string} filePath - 文件路径
   * @returns {boolean} - 文件是否可读
   */
  static isReadableSync(filePath) {
    if (!filePath) {
      return false
    }

    try {
      fs.accessSync(filePath, fs.constants.R_OK)
      return true
    } catch {
      return false
    }
  }

  /**
   * 创建文件FileBox对象
   * @param {string} filePath 文件路径
   * @returns {Object|null} 文件对象
   */
  static createFileBox(filePath) {
    try {
      if (filePath && FileUtils.fileExistsSync(filePath)) {
        // 返回一个简单的文件对象，因为FileBox未定义
        return {
          path: filePath,
          name: path.basename(filePath),
        }
      }
      return null
    } catch (error) {
      log.c.error(`创建文件对象失败`, { error: error.message })
      return null
    }
  }

  /**
   * 处理致命错误，记录日志并退出进程
   * @param {string} message - 错误消息
   * @param {number} exitCode - 退出代码，默认为1
   */
  static handleFatalError(message, exitCode = 1) {
    log.fatal(message, exitCode)
  }
}

// 导出单独的函数，方便直接导入使用
export const {
  deleteFile,
  fileExists,
  fileExistsSync,
  ensureDir,
  ensureDirSync,
  ensureDirs,
  ensureDirsSync,
  copyFile,
  copyFileSync,
  readFile,
  readFileSync,
  writeFile,
  writeFileSync,
  isReadable,
  isReadableSync,
  createFileBox,
  handleFatalError,
} = FileUtils
