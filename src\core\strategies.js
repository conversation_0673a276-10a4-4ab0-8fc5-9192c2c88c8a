/**
 * 关键词策略模块
 * 使用策略模式处理不同类型关键词的识别和处理
 */

import { isValidArray, isValidString } from '../utils/dataValidators.js'
import { log } from '../utils/logging.js'

// 统一配置管理
const CONFIG = {
  defaultTypes: [
    'normal', // 普通关键词（原textKeywords）
    'transport', // 厂车关键词
    'urea', // 尿素关键词
    'details_only', // 仅录入详情关键词
    'article', // 微信公众号文章处理
    'imported', // 进口玉米的特殊子类型
  ],
  messageTemplates: {
    start: type => `AI处理${type}消息中`,
    success: type => `${type}消息处理完成`,
    error: type => `${type}消息处理失败`,
  },
}

// 类型工具函数
const TypeUtils = {
  normalize: type => type?.trim().toLowerCase(),
  validate: type => isValidString(type),
  toUpperCase: type => type?.toUpperCase(),
  toCamelCase: type => {
    if (!type) return ''
    return type.includes('_')
      ? type
          .split('_')
          .reduce(
            (acc, part, index) =>
              acc + (index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1)),
            '',
          )
      : type
  },
}

// 简化的关键词类型管理
class KeywordTypeManager {
  constructor() {
    this.types = new Map()
    this._initializeDefaultTypes()
  }

  _initializeDefaultTypes() {
    CONFIG.defaultTypes.forEach(type => {
      this.types.set(type.toUpperCase(), type)
    })
  }

  register(typeName) {
    if (!TypeUtils.validate(typeName)) {
      throw new Error('类型名称必须是非空字符串')
    }

    const normalized = TypeUtils.normalize(typeName)
    const upper = TypeUtils.toUpperCase(normalized)

    if (this.types.has(upper)) {
      return false // 已存在
    }

    this.types.set(upper, normalized)
    return true
  }

  get(key) {
    return this.types.get(key?.toString())
  }

  has(key) {
    return this.types.has(key?.toString())
  }

  getAll() {
    return Array.from(this.types.values())
  }

  getAllKeys() {
    return Array.from(this.types.keys())
  }
}

const typeManager = new KeywordTypeManager()

// 创建简化的 KEYWORD_TYPES 对象
const KEYWORD_TYPES = new Proxy(
  {},
  {
    get: (target, prop) => typeManager.get(prop),
    has: (target, prop) => typeManager.has(prop),
    ownKeys: () => typeManager.getAllKeys(),
    getOwnPropertyDescriptor: (target, prop) => {
      if (typeManager.has(prop)) {
        return {
          value: typeManager.get(prop),
          enumerable: true,
          configurable: true,
          writable: false,
        }
      }
      return undefined
    },
  },
)

// 策略配置工厂 - 消除重复配置
const StrategyConfigFactory = {
  /**
   * 创建标准策略配置
   * @param {string} type - 策略类型
   * @param {Object} customMessages - 自定义消息（可选）
   * @returns {Object} 策略配置
   */
  create(type, customMessages = {}) {
    if (!TypeUtils.validate(type)) {
      throw new Error('策略类型必须是有效字符串')
    }

    const normalizedType = TypeUtils.normalize(type)
    const camelCaseType = TypeUtils.toCamelCase(normalizedType)

    return {
      keywordsProp: `${camelCaseType}Keywords`,
      promptType: normalizedType,
      messages: {
        start: customMessages.start || CONFIG.messageTemplates.start(normalizedType),
        success: customMessages.success || CONFIG.messageTemplates.success(normalizedType),
        error: customMessages.error || CONFIG.messageTemplates.error(normalizedType),
      },
    }
  },

  /**
   * 创建继承配置 - 仅用于 imported 类型
   * @param {string} baseType - 基础类型
   * @param {string} newType - 新类型
   * @returns {Object} 继承的策略配置
   */
  createInherited(baseType, newType) {
    const baseConfig = this.create(baseType)
    const newConfig = this.create(newType)

    return {
      ...baseConfig,
      ...newConfig,
      businessType: baseType, // 保留业务类型引用
    }
  },
}

// 使用工厂创建策略配置缓存
class StrategyConfigManager {
  constructor() {
    this.configs = new Map()
    this._initializeDefaultConfigs()
  }

  _initializeDefaultConfigs() {
    // 标准配置
    const standardConfigs = {
      normal: {},
      transport: { start: '厂车', success: '厂车', error: '厂车' },
      urea: { start: '尿素', success: '尿素', error: '尿素' },
      details_only: { start: '详情录入', success: '详情录入', error: '详情录入' },
      article: { start: '公众号文章', success: '公众号文章', error: '公众号文章' },
    }

    // 创建标准配置
    Object.entries(standardConfigs).forEach(([type, messages]) => {
      this.configs.set(type, StrategyConfigFactory.create(type, messages))
    })

    // 创建继承配置
    this.configs.set('imported', StrategyConfigFactory.createInherited('normal', 'imported'))
  }

  get(type) {
    const normalizedType = TypeUtils.normalize(type)
    if (!this.configs.has(normalizedType)) {
      // 动态创建配置
      const config = StrategyConfigFactory.create(normalizedType)
      this.configs.set(normalizedType, config)
    }
    return this.configs.get(normalizedType)
  }

  register(type, config) {
    const normalizedType = TypeUtils.normalize(type)
    this.configs.set(normalizedType, config)
  }
}

const configManager = new StrategyConfigManager()

// STRATEGY_CONFIG 已删除 - 无外部使用，通过 configManager 内部管理

/**
 * 关键词策略基类 - 优化版
 * 提供关键词处理的基本接口和通用实现
 */
class KeywordStrategy {
  /**
   * 创建关键词策略
   * @param {string} type - 关键词类型
   * @param {Object} config - 策略配置
   */
  constructor(type, config) {
    if (!TypeUtils.validate(type)) {
      throw new Error(`无效的关键词类型: ${type}`)
    }

    this.type = TypeUtils.normalize(type)
    this.config = config || configManager.get(this.type)

    // 验证配置完整性
    this._validateConfig()
  }

  /**
   * 验证配置完整性
   * @private
   */
  _validateConfig() {
    const requiredProps = ['keywordsProp', 'messages']
    const missingProps = requiredProps.filter(prop => !this.config[prop])

    if (missingProps.length > 0) {
      throw new Error(`策略配置缺少必要属性: ${missingProps.join(', ')}`)
    }
  }

  /**
   * 获取关键词列表 - 优化版
   * @param {Object} keywords - 关键词配置对象
   * @returns {Array} - 关键词列表
   */
  getKeywords(keywords) {
    if (!keywords) return []

    const keywordsArray = keywords[this.config.keywordsProp]
    return isValidArray(keywordsArray) ? keywordsArray : []
  }

  /**
   * 构建处理提示词 - 简化版
   * @param {boolean} isImage - 是否为图片处理
   * @returns {Function} - 提示词构建函数
   */
  buildPrompt(isImage = false) {
    return service => {
      if (!service?.buildPrompt) {
        throw new Error('服务实例必须提供 buildPrompt 方法')
      }
      return service.buildPrompt(isImage, this.type)
    }
  }

  /**
   * 获取处理消息
   * @returns {Object} - 处理过程中的消息
   */
  getMessages() {
    return { ...this.config.messages } // 返回副本避免意外修改
  }

  /**
   * 获取策略类型
   * @returns {string} - 策略类型
   */
  getType() {
    return this.type
  }

  /**
   * 获取完整配置
   * @returns {Object} - 策略配置
   */
  getConfig() {
    return { ...this.config } // 返回副本
  }
}

/**
 * 策略创建工厂函数 - 优化版
 * @param {string} type - 策略类型
 * @param {Object} customConfig - 自定义配置（可选）
 * @returns {KeywordStrategy} - 策略实例
 */
const createStrategy = (type, customConfig = null) => {
  if (!TypeUtils.validate(type)) {
    throw new Error('策略类型不能为空')
  }

  const normalizedType = TypeUtils.normalize(type)
  const defaultConfig = configManager.get(normalizedType)
  const config = customConfig ? { ...defaultConfig, ...customConfig } : defaultConfig

  return new KeywordStrategy(normalizedType, config)
}

/**
 * 高性能策略管理器 - 带缓存优化
 */
class StrategyManager {
  constructor() {
    this.strategies = new Map()
    this.cache = new Map() // 添加缓存层
    this.defaultType = 'normal'
    this._initializeDefaultStrategies()
  }

  /**
   * 初始化默认策略
   * @private
   */
  _initializeDefaultStrategies() {
    typeManager.getAll().forEach(type => {
      const strategy = createStrategy(type)
      this.strategies.set(type, strategy)
    })
  }

  /**
   * 注册策略 - 支持链式调用
   * @param {string} type - 关键词类型
   * @param {KeywordStrategy} strategy - 策略实例
   * @returns {StrategyManager} - 管理器实例
   */
  register(type, strategy) {
    if (!(strategy instanceof KeywordStrategy)) {
      throw new Error('策略必须是KeywordStrategy的实例')
    }

    const normalizedType = TypeUtils.normalize(type)
    this.strategies.set(normalizedType, strategy)
    this.cache.clear() // 清除缓存
    return this
  }

  /**
   * 获取策略 - 带缓存优化
   * @param {string} type - 关键词类型
   * @returns {KeywordStrategy} 策略实例
   */
  getStrategy(type) {
    if (this.cache.has(type)) {
      return this.cache.get(type)
    }

    const normalizedType = TypeUtils.normalize(type)
    let strategy = this.strategies.get(normalizedType)

    // 回退到默认策略
    if (!strategy) {
      log.c.warn(`未找到类型为 ${type} 的策略，使用默认策略`)
      strategy = this.strategies.get(this.defaultType)
    }

    // 缓存结果
    if (strategy) {
      this.cache.set(type, strategy)
    }

    return strategy
  }

  /**
   * 注册自定义策略 - 简化版
   * @param {string} type - 关键词类型
   * @param {Object} config - 策略配置
   * @returns {KeywordStrategy} - 新注册的策略实例
   */
  registerCustomStrategy(type, config) {
    if (!TypeUtils.validate(type)) {
      throw new Error('策略类型不能为空')
    }

    // 注册类型到类型管理器
    typeManager.register(type)

    // 注册配置到配置管理器
    if (config) {
      configManager.register(type, config)
    }

    const strategy = createStrategy(type, config)
    this.register(type, strategy)
    return strategy
  }
}

// 创建单例管理器
const strategyManager = new StrategyManager()

/**
 * 统一的API接口 - 优化版
 */
const StrategyAPI = {
  /**
   * 注册新的关键词类型
   * @param {string} typeName - 类型名称
   * @returns {boolean} - 注册是否成功
   */
  registerType: typeName => {
    try {
      if (!TypeUtils.validate(typeName)) {
        log.c.error('类型名称必须是非空字符串')
        return false
      }

      const normalizedType = TypeUtils.normalize(typeName)

      // 检查是否已存在
      if (typeManager.has(normalizedType.toUpperCase())) {
        log.c.warn(`关键词类型 ${normalizedType} 已存在，无需重复注册`)
        return true
      }

      // 注册到类型管理器
      const registered = typeManager.register(normalizedType)
      if (!registered) {
        return false
      }

      // 创建并注册策略
      const strategy = createStrategy(normalizedType)
      strategyManager.register(normalizedType, strategy)
      log.c.info(`成功注册新的关键词类型: ${normalizedType}`)
      return true
    } catch (error) {
      log.c.error(`注册关键词类型 ${typeName} 失败:`, error)
      return false
    }
  },
}

// 核心API函数 - 直接导出使用的方法
const getKeywordStrategy = type => strategyManager.getStrategy(type)
const registerKeywordType = typeName => StrategyAPI.registerType(typeName)

export { KEYWORD_TYPES, getKeywordStrategy, registerKeywordType }
