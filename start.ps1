# 微信消息监听器 - PowerShell 启动脚本
# 修复版本，正确处理目录

# Set working directory to script location
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $ScriptDir

# Set console encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Set window title
$Host.UI.RawUI.WindowTitle = "微信消息监听器 - PowerShell 启动脚本"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    微信消息监听器 - PowerShell 启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "工作目录: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

# Check Node.js
Write-Host "[1/6] 检查Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
        
        # Check Node.js version
        $majorVersion = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
        if ($majorVersion -lt 18) {
            Write-Host "❌ 错误：Node.js 版本过低" -ForegroundColor Red
            Write-Host "当前版本：$nodeVersion，需要：v18.0.0 或更高版本" -ForegroundColor Red
            Write-Host "请升级 Node.js：https://nodejs.org/" -ForegroundColor Yellow
            Read-Host "按回车键退出"
            exit 1
        }
        Write-Host "✅ Node.js版本符合要求" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "❌ 错误：未检测到 Node.js" -ForegroundColor Red
    Write-Host "请安装 Node.js 18.0.0 或更高版本" -ForegroundColor Red
    Write-Host "下载地址：https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# Check npm
Write-Host "[2/6] 检查npm..." -ForegroundColor Yellow
try {
    $npmVersion = & npm --version 2>$null
    if ($npmVersion) {
        Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm not available"
    }
} catch {
    Write-Host "❌ 错误：npm 不可用" -ForegroundColor Red
    Write-Host "请重新安装 Node.js" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# Check project files
Write-Host "[3/6] 检查项目文件..." -ForegroundColor Yellow
if (!(Test-Path "package.json")) {
    Write-Host "❌ 错误：未找到 package.json" -ForegroundColor Red
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Red
    Write-Host "请确保此脚本在项目根目录中运行" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

if (!(Test-Path "src\app\start.js")) {
    Write-Host "❌ 错误：未找到 src\app\start.js" -ForegroundColor Red
    Write-Host "请确保项目文件完整" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 项目文件检查完成" -ForegroundColor Green

# Check dependencies
Write-Host "[4/6] 检查依赖..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "📦 正在安装依赖..." -ForegroundColor Blue
    & npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 错误：依赖安装失败" -ForegroundColor Red
        Write-Host "请检查网络连接" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    Write-Host "✅ 依赖安装成功" -ForegroundColor Green
} else {
    Write-Host "✅ 依赖已安装" -ForegroundColor Green
}

# Check environment configuration
Write-Host "[5/6] 检查环境配置..." -ForegroundColor Yellow
if (!(Test-Path ".env") -and !(Test-Path ".env.production")) {
    Write-Host "❌ 错误：缺少环境配置文件" -ForegroundColor Red
    Write-Host "请根据 .env.example 创建 .env 或 .env.production 文件" -ForegroundColor Red
    Write-Host "并配置必要参数（服务器地址、认证信息、AI 密钥等）" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 环境配置文件存在" -ForegroundColor Green

# 选择运行模式
Write-Host ""
Write-Host "[6/6] 选择运行模式..." -ForegroundColor Yellow
Write-Host "选择运行模式:" -ForegroundColor Cyan
Write-Host "1. 生产模式 (推荐)" -ForegroundColor White
Write-Host "2. 开发模式 (调试用)" -ForegroundColor White
Write-Host ""
$mode = Read-Host "请输入选项 (1-2)"

switch ($mode) {
    "1" {
        Write-Host ""
        Write-Host "🚀 正在启动生产模式..." -ForegroundColor Green
        Write-Host ""
        & npm start
    }
    "2" {
        Write-Host ""
        Write-Host "🛠️ 正在启动开发模式..." -ForegroundColor Blue
        Write-Host ""
        & npm run dev
    }
    default {
        Write-Host ""
        Write-Host "❌ 无效选项，将使用默认的生产模式" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "🚀 正在启动生产模式..." -ForegroundColor Green
        Write-Host ""
        & npm start
    }
}

Write-Host ""
Write-Host "程序已退出" -ForegroundColor Gray
Read-Host "按回车键退出"