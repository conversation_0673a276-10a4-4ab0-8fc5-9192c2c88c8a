// src/core/configLoader.js
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { safeAsync, safeJsonParse } from '../utils/errorHandler.js'
import {
  isNonEmptyArray,
  isValidString,
  isNonEmptyObject,
  isValidFunction,
} from '../utils/dataValidators.js'
import { FileUtils, fileExists, ensureDir, readFile, writeFile } from '../utils/fileUtils.js'

// 统一配置管理
const CONFIG = {
  paths: {
    root: path.join(path.dirname(fileURLToPath(import.meta.url)), '../../config'),
    get prompts() {
      return path.join(this.root, 'prompts')
    },
  },
  defaults: {
    types: ['normal', 'transport', 'urea', 'details_only', 'article'],
    specialOrder: ['transport', 'urea', 'details_only'],
    promptExtension: '.md',
  },
  files: {
    contacts: 'contacts.json',
    keywords: 'keywords.json',
    priorities: 'priorities.json',
    aiProcessing: 'aiProcessing.json',
    aliases: 'aliases.json',
  },
}

// 统一验证器（扩展现有的 dataValidators）
const Validator = {
  isValidType: type => isValidString(type),
  isValidArray: arr => isNonEmptyArray(arr),
  isValidConfig: config => isNonEmptyObject(config),
  isValidBoolean: value => typeof value === 'boolean',
}

// 统一配置管理器
class ConfigManager {
  constructor() {
    this.cache = new Map()
  }

  async load(fileName, defaultValue = {}) {
    const filePath = path.join(CONFIG.paths.root, fileName)

    return safeAsync(
      async () => {
        const data = (await readFile(filePath)) || JSON.stringify(defaultValue)
        return safeJsonParse(data, defaultValue, `解析配置文件 ${fileName}`)
      },
      defaultValue,
      `加载配置文件 ${fileName} 失败，使用默认配置`,
    )
  }

  async save(fileName, config) {
    const filePath = path.join(CONFIG.paths.root, fileName)
    const content = JSON.stringify(config, null, 2)
    return await writeFile(filePath, content)
  }

  async getConfigWithSaver(fileName, defaultValue = {}) {
    const config = await this.load(fileName, defaultValue)
    return {
      config,
      save: async newConfig => await this.save(fileName, newConfig || config),
    }
  }
}

const configManager = new ConfigManager()

/**
 * 安全地导入策略模块以避免循环依赖
 */
const safeImportStrategies = () =>
  safeAsync(
    () => import('./strategies.js'),
    { KEYWORD_TYPES: {}, registerKeywordType: () => false },
    '无法导入策略模块',
  )

// 类型管理器
class TypeManager {
  async getTypes() {
    return safeAsync(
      async () => {
        const { KEYWORD_TYPES } = await safeImportStrategies()
        return Object.values(KEYWORD_TYPES).map(type => type.toLowerCase())
      },
      CONFIG.defaults.types,
      '无法获取关键词类型',
    )
  }

  getPropertyName(typeName) {
    if (!Validator.isValidType(typeName)) return null

    // 处理带下划线的类型名称，转换为驼峰式
    const normalizedType = typeName.includes('_')
      ? typeName
          .split('_')
          .reduce(
            (acc, part, index) =>
              acc + (index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1)),
            '',
          )
      : typeName

    return `${normalizedType}Keywords`
  }

  getPromptFileName(typeName) {
    return Validator.isValidType(typeName) ? `${typeName}_prompt` : null
  }

  async registerType(typeName) {
    if (!Validator.isValidType(typeName)) return false

    return safeAsync(
      async () => {
        const { registerKeywordType } = await safeImportStrategies()
        return registerKeywordType(typeName.toLowerCase())
      },
      false,
      `注册类型 ${typeName} 失败`,
    )
  }
}

const typeManager = new TypeManager()

// 统一的加载器工厂
const createLoader =
  (fileName, processor, defaultValue = {}) =>
  async () => {
    const config = await configManager.load(fileName, defaultValue)
    return processor(config)
  }

// 配置处理器
const ConfigProcessors = {
  contacts: config => ({ contacts: config.contacts || {} }),

  keywords: async config => {
    const result = {}
    const types = await typeManager.getTypes()

    types.forEach(type => {
      const propName = typeManager.getPropertyName(type)
      if (propName) {
        result[propName] = Validator.isValidArray(config[propName]) ? config[propName] : []
      }
    })

    return result
  },

  aliases: config => config,
}

/**
 * 加载关键词类型优先级配置
 * @returns {object} 关键词类型优先级配置对象
 */
// 扩展配置处理器
Object.assign(ConfigProcessors, {
  priorities: async config => {
    const defaultPriorities = await safeAsync(
      async () => {
        const allTypes = await typeManager.getTypes()
        const normalType = 'normal'

        // 特殊类型按顺序，其余类型按原顺序，normal最后
        return [
          ...CONFIG.defaults.specialOrder.filter(type => allTypes.includes(type)),
          ...allTypes.filter(
            type => !CONFIG.defaults.specialOrder.includes(type) && type !== normalType,
          ),
          ...(allTypes.includes(normalType) ? [normalType] : []),
        ]
      },
      CONFIG.defaults.specialOrder.concat(['normal']),
    )

    return {
      keywordTypePriorities: Validator.isValidArray(config.keywordTypePriorities)
        ? config.keywordTypePriorities
        : defaultPriorities,
    }
  },

  aiProcessing: config => ({
    enabled: Validator.isValidBoolean(config.enabled) ? config.enabled : true,
    typeSettings: config.typeSettings || {},
  }),
})

const loadPriorities = createLoader(CONFIG.files.priorities, ConfigProcessors.priorities)

/**
 * 加载所有提示词文件。
 * @returns {object} 包含所有提示词内容的映射对象
 */
async function loadPrompts() {
  // 确保提示词目录存在
  await ensureDir(CONFIG.paths.prompts)

  // 读取目录中的所有文件
  const promptFiles = await safeAsync(
    async () => await fs.promises.readdir(CONFIG.paths.prompts),
    [],
    '读取提示词目录失败',
  )

  // 加载提示词文件
  const prompts = {}
  const promptLengths = {}
  for (const file of promptFiles) {
    if (file.endsWith(CONFIG.defaults.promptExtension)) {
      await safeAsync(
        async () => {
          const filePath = path.join(CONFIG.paths.prompts, file)
          const content = await readFile(filePath)
          const promptName = file.replace(CONFIG.defaults.promptExtension, '')
          prompts[promptName] = content
          promptLengths[promptName] = `${content.length}字符`
        },
        null,
        `加载提示词文件 ${file} 失败`,
        // 加载失败时记录长度为 -1
        err => {
          const promptName = file.replace(CONFIG.defaults.promptExtension, '')
          promptLengths[promptName] = -1
        },
      )
    }
  }
  log.c.info('已加载Prompts: ' + JSON.stringify(promptLengths, null, 2))

  // 检查是否需要创建默认提示词文件
  await safeAsync(
    async () => {
      const { KEYWORD_TYPES } = await safeImportStrategies()

      // 遍历所有关键词类型，检查是否存在对应的提示词文件
      for (const value of Object.values(KEYWORD_TYPES)) {
        const typeName = value.toLowerCase()
        const promptFileName = typeManager.getPromptFileName(typeName)

        // 如果提示词不存在，记录警告
        if (!prompts[promptFileName]) {
          log.c.warn(
            `未找到 ${typeName} 类型的提示词文件 (${promptFileName}${CONFIG.defaults.promptExtension})，该类型将使用默认提示词`,
          )
        }
      }
    },
    null,
    '无法检查提示词文件完整性',
  )

  return prompts
}

/**
 * 加载AI处理配置
 * @returns {Promise<object>} AI处理配置对象
 */
async function loadAiProcessingConfig() {
  const { config } = await configManager.load(CONFIG.files.aiProcessing)
  const result = {
    enabled: typeof config.enabled === 'boolean' ? config.enabled : true,
    typeSettings: {},
  }

  await safeAsync(
    async () => {
      const allTypes = await typeManager.getTypes()
      // 为所有类型设置默认配置
      allTypes.forEach(typeName => {
        result.typeSettings[typeName] =
          typeof config.typeSettings?.[typeName] === 'boolean'
            ? config.typeSettings[typeName]
            : true
      })
    },
    () => {
      // 回退到硬编码方式
      CONFIG.defaults.types.forEach(type => {
        result.typeSettings[type] =
          typeof config.typeSettings?.[type] === 'boolean' ? config.typeSettings[type] : true
      })
    },
  )

  return result
}

/**
 * 创建提示词文件
 * @param {string} typeName - 消息类型名称
 * @param {string} promptTemplate - 提示词模板
 */
async function createPromptFile(typeName, promptTemplate) {
  // 确保提示词目录存在
  await ensureDir(CONFIG.paths.prompts)

  // 确定提示词文件名
  const promptFileName = typeManager.getPromptFileName(typeName)
  const promptFilePath = path.join(
    CONFIG.paths.prompts,
    `${promptFileName}${CONFIG.defaults.promptExtension}`,
  )

  // 检查文件是否已存在
  const exists = await fileExists(promptFilePath)

  if (!exists) {
    // 创建默认提示词模板
    const defaultTemplate =
      promptTemplate ||
      `# ${typeName.toUpperCase()} 消息处理提示词

## 核心目标
从${typeName}消息中提取关键信息并生成结构化数据。

## 输出格式
请以JSON格式输出提取的信息:

\`\`\`json
[
  {
    "key": "value"
  }
]
\`\`\`

## 重要规则
1. 保持客观，只提取文本中明确提及的信息
2. 请严格按照以上规则处理输入文本，仅输出JSON数组，不要添加任何解释性文字
`

    // 保存提示词文件
    await writeFile(promptFilePath, defaultTemplate)
  }
}

/**
 * 更新关键词配置
 * @param {string} typeName - 消息类型名称
 * @param {Array<string>} keywords - 关键词列表
 */
async function updateKeywords(typeName, keywords) {
  const { config, save } = await configManager.getConfigWithSaver(CONFIG.files.keywords)
  const keywordsProp = typeManager.getPropertyName(typeName)
  config[keywordsProp] = Validator.isValidArray(keywords) ? keywords : []
  await save(config)
}

/**
 * 更新AI处理配置
 * @param {string} typeName - 消息类型名称
 * @param {boolean} enableAi - 是否启用AI处理
 */
async function updateAiProcessingConfig(typeName, enableAi) {
  const { config, save } = await configManager.getConfigWithSaver(CONFIG.files.aiProcessing, {
    enabled: true,
    typeSettings: {},
  })

  // 确保配置结构完整
  if (!config.typeSettings) {
    config.typeSettings = {}
  }

  // 更新配置
  config.typeSettings[typeName] = typeof enableAi === 'boolean' ? enableAi : true
  await save(config)
}

/**
 * 更新优先级配置
 * @param {string} typeName - 消息类型名称
 * @param {number} priority - 优先级（保留参数以保持接口兼容性）
 */
async function updatePriorities(typeName, priority) {
  return safeAsync(
    async () => {
      // 使用 PriorityManager 进行输入验证
      if (!Validator.isValidType(typeName)) {
        throw new Error('无效的消息类型名称')
      }

      const { config, save } = await configManager.getConfigWithSaver(CONFIG.files.priorities, {
        keywordTypePriorities: PriorityManager.getDefaultPriorities(),
      })

      // 验证当前配置的完整性
      const currentPriorities = config.keywordTypePriorities || []
      if (!PriorityManager.validatePriorities(currentPriorities)) {
        log.c.warn('检测到优先级配置异常，将重置为默认配置')
        config.keywordTypePriorities = PriorityManager.getDefaultPriorities()
      }

      // 使用优化的优先级更新逻辑
      const updatedPriorities = PriorityManager.updateTypePriority(
        config.keywordTypePriorities,
        typeName,
      )

      // 只有在实际发生变化时才保存
      if (JSON.stringify(updatedPriorities) !== JSON.stringify(config.keywordTypePriorities)) {
        config.keywordTypePriorities = updatedPriorities
        await save(config)
        log.c.info(`已更新优先级配置: ${typeName}`)
      }

      return true
    },
    false,
    `更新优先级配置失败: ${typeName}`,
  )
}

// 优先级管理器 - 职责单一的优先级操作
const PriorityManager = {
  /**
   * 更新类型优先级
   * @param {Array} priorities - 当前优先级列表
   * @param {string} typeName - 要更新的类型名称
   * @returns {Array} 更新后的优先级列表
   */
  updateTypePriority(priorities, typeName) {
    // 修复：允许空数组，只检查是否为数组
    if (!Array.isArray(priorities)) {
      priorities = []
    }

    // 验证类型名称
    if (!Validator.isValidType(typeName)) {
      throw new Error(`无效的类型名称: ${typeName}`)
    }

    // 如果类型已存在且位置正确，直接返回
    if (this._isTypeInCorrectPosition(priorities, typeName)) {
      return [...priorities]
    }

    // 移除已存在的类型（避免重复）
    const filtered = priorities.filter(type => type !== typeName)

    // 根据类型特性插入到合适位置
    return this._insertTypeAtCorrectPosition(filtered, typeName)
  },

  /**
   * 检查类型是否已在正确位置
   * @param {Array} priorities - 优先级列表
   * @param {string} typeName - 类型名称
   * @returns {boolean} 是否在正确位置
   */
  _isTypeInCorrectPosition(priorities, typeName) {
    const currentIndex = priorities.indexOf(typeName)
    if (currentIndex === -1) return false

    if (typeName === 'normal') {
      // normal 应该在最后
      return currentIndex === priorities.length - 1
    } else {
      // 其他类型应该在 normal 之前
      const normalIndex = priorities.indexOf('normal')
      return normalIndex === -1 || currentIndex < normalIndex
    }
  },

  /**
   * 在正确位置插入类型
   * @param {Array} priorities - 过滤后的优先级列表
   * @param {string} typeName - 要插入的类型名称
   * @returns {Array} 插入后的优先级列表
   */
  _insertTypeAtCorrectPosition(priorities, typeName) {
    // normal 类型始终在最后
    if (typeName === 'normal') {
      return [...priorities, typeName]
    }

    // 其他类型插入到 normal 之前
    const normalIndex = priorities.indexOf('normal')
    if (normalIndex >= 0) {
      return [...priorities.slice(0, normalIndex), typeName, ...priorities.slice(normalIndex)]
    }

    // 如果没有 normal 类型，直接添加到末尾
    return [...priorities, typeName]
  },

  /**
   * 获取默认优先级列表
   * @returns {Array} 默认优先级列表
   */
  getDefaultPriorities() {
    return [...CONFIG.defaults.specialOrder, 'normal']
  },

  /**
   * 验证优先级列表的完整性
   * @param {Array} priorities - 优先级列表
   * @returns {boolean} 是否有效
   */
  validatePriorities(priorities) {
    if (!Array.isArray(priorities)) return false

    // 检查是否有重复项
    const uniqueItems = new Set(priorities)
    if (uniqueItems.size !== priorities.length) return false

    // 检查 normal 是否在最后（如果存在）
    const normalIndex = priorities.indexOf('normal')
    if (normalIndex >= 0 && normalIndex !== priorities.length - 1) return false

    return true
  },
}

/**
 * 为新消息类型创建配置文件
 * @param {string} typeName - 消息类型名称
 * @param {Object} options - 配置选项
 * @returns {Promise<boolean>} - 是否创建成功
 */
async function createMessageTypeConfig(typeName, options = {}) {
  if (!typeName) {
    log.c.error('消息类型名称不能为空')
    return false
  }

  // 标准化类型名称为小写
  const normalizedType = typeName.toLowerCase()
  const {
    keywords = [],
    promptTemplate = '',
    enableAi = true,
    priority = 100, // 默认优先级
  } = options

  return await safeAsync(
    async () => {
      // 1. 注册关键词类型到策略系统
      await safeAsync(
        async () => {
          const { registerKeywordType } = await safeImportStrategies()
          const success = registerKeywordType(normalizedType)
          if (!success) {
            log.c.warn(`注册关键词类型 ${normalizedType} 失败，但将继续创建配置文件`)
          }
        },
        null,
        '无法导入或调用registerKeywordType方法',
      )

      // 2. 更新各种配置
      await updateKeywords(normalizedType, keywords)
      await createPromptFile(normalizedType, promptTemplate)
      await updateAiProcessingConfig(normalizedType, enableAi)
      await updatePriorities(normalizedType, priority)

      log.c.success(`成功为消息类型 ${normalizedType} 创建配置`)
      return true
    },
    false,
    `为消息类型 ${normalizedType} 创建配置失败`,
  )
}

/**
 * 加载所有配置
 * @returns {Promise<Object>} 包含所有配置的对象
 */
// 统一的加载器实例
const loaders = {
  contacts: createLoader(CONFIG.files.contacts, ConfigProcessors.contacts),
  keywords: createLoader(CONFIG.files.keywords, ConfigProcessors.keywords),
  priorities: loadPriorities,
  prompts: loadPrompts,
  aiProcessing: createLoader(CONFIG.files.aiProcessing, ConfigProcessors.aiProcessing),
  aliases: createLoader(CONFIG.files.aliases, ConfigProcessors.aliases),
}

// 导出的加载函数
export const loadContacts = loaders.contacts
export const loadKeywords = loaders.keywords
export const loadAliases = loaders.aliases

export async function loadAllConfigs() {
  const [contacts, keywords, priorities, prompts, aiProcessing, aliases] = await Promise.all([
    loaders.contacts(),
    loaders.keywords(),
    loaders.priorities(),
    loaders.prompts(),
    loaders.aiProcessing(),
    loaders.aliases(),
  ])

  return { contacts, keywords, priorities, prompts, aiProcessing, aliases }
}
