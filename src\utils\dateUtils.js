/**
 * 统一的日期工具模块
 * 提供项目中所有日期相关的工具函数，确保时区和格式的一致性
 */

import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc.js'
import timezone from 'dayjs/plugin/timezone.js'

// 扩展 dayjs 插件
dayjs.extend(utc)
dayjs.extend(timezone)

// 项目统一配置
const DATE_CONFIG = {
  TIMEZONE: 'Asia/Shanghai',
  FORMATS: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIME: 'HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss.SSS',
  },
}

/**
 * 获取当前日期（项目标准格式）
 * @returns {string} 当前日期 YYYY-MM-DD 格式
 */
export function getCurrentDate() {
  return dayjs().tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.DATE)
}

/**
 * 获取当前日期时间（项目标准格式）
 * @returns {string} 当前日期时间 YYYY-MM-DD HH:mm:ss 格式
 */
export function getCurrentDateTime() {
  return dayjs().tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.DATETIME)
}

/**
 * 获取当前时间（项目标准格式）
 * @returns {string} 当前时间 HH:mm:ss 格式
 */
export function getCurrentTime() {
  return dayjs().tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.TIME)
}

/**
 * 获取当前时间戳（项目标准格式）
 * @returns {string} 当前时间戳 YYYY-MM-DD HH:mm:ss.SSS 格式
 */
export function getCurrentTimestamp() {
  return dayjs().tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.TIMESTAMP)
}

/**
 * 格式化时间戳为日期
 * @param {number} timestamp - Unix 时间戳（秒或毫秒）
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {string} 格式化后的日期字符串
 */
export function formatTimestamp(timestamp, format = DATE_CONFIG.FORMATS.DATE) {
  // 自动检测时间戳格式（秒或毫秒）
  const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
  return dayjs(ts).tz(DATE_CONFIG.TIMEZONE).format(format)
}

/**
 * 格式化日期字符串
 * @param {string|Date|dayjs} date - 日期对象或字符串
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = DATE_CONFIG.FORMATS.DATE) {
  return dayjs(date).tz(DATE_CONFIG.TIMEZONE).format(format)
}

/**
 * 检查日期是否为今天
 * @param {string|Date|dayjs} date - 要检查的日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const inputDate = dayjs(date).tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.DATE)
  const today = getCurrentDate()
  return inputDate === today
}

/**
 * 检查日期是否为昨天
 * @param {string|Date|dayjs} date - 要检查的日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  const inputDate = dayjs(date).tz(DATE_CONFIG.TIMEZONE).format(DATE_CONFIG.FORMATS.DATE)
  const yesterday = dayjs()
    .tz(DATE_CONFIG.TIMEZONE)
    .subtract(1, 'day')
    .format(DATE_CONFIG.FORMATS.DATE)
  return inputDate === yesterday
}

/**
 * 获取相对日期
 * @param {number} days - 天数偏移（正数为未来，负数为过去）
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {string} 相对日期字符串
 */
export function getRelativeDate(days, format = DATE_CONFIG.FORMATS.DATE) {
  return dayjs().tz(DATE_CONFIG.TIMEZONE).add(days, 'day').format(format)
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date|dayjs} date1 - 第一个日期
 * @param {string|Date|dayjs} date2 - 第二个日期
 * @returns {number} 天数差（date1 - date2）
 */
export function getDaysDifference(date1, date2) {
  const d1 = dayjs(date1).tz(DATE_CONFIG.TIMEZONE)
  const d2 = dayjs(date2).tz(DATE_CONFIG.TIMEZONE)
  return d1.diff(d2, 'day')
}

/**
 * 验证日期字符串格式
 * @param {string} dateString - 日期字符串
 * @param {string} format - 期望的格式，默认为 YYYY-MM-DD
 * @returns {boolean} 是否为有效格式
 */
export function isValidDateFormat(dateString, format = DATE_CONFIG.FORMATS.DATE) {
  return dayjs(dateString, format, true).isValid()
}

/**
 * 获取日期范围内的所有日期
 * @param {string|Date|dayjs} startDate - 开始日期
 * @param {string|Date|dayjs} endDate - 结束日期
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {string[]} 日期数组
 */
export function getDateRange(startDate, endDate, format = DATE_CONFIG.FORMATS.DATE) {
  const start = dayjs(startDate).tz(DATE_CONFIG.TIMEZONE)
  const end = dayjs(endDate).tz(DATE_CONFIG.TIMEZONE)
  const dates = []

  let current = start
  while (current.isSameOrBefore(end, 'day')) {
    dates.push(current.format(format))
    current = current.add(1, 'day')
  }

  return dates
}

/**
 * 获取本周的开始和结束日期
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {Object} { start: string, end: string }
 */
export function getThisWeek(format = DATE_CONFIG.FORMATS.DATE) {
  const now = dayjs().tz(DATE_CONFIG.TIMEZONE)
  const start = now.startOf('week').format(format)
  const end = now.endOf('week').format(format)
  return { start, end }
}

/**
 * 获取本月的开始和结束日期
 * @param {string} format - 输出格式，默认为 YYYY-MM-DD
 * @returns {Object} { start: string, end: string }
 */
export function getThisMonth(format = DATE_CONFIG.FORMATS.DATE) {
  const now = dayjs().tz(DATE_CONFIG.TIMEZONE)
  const start = now.startOf('month').format(format)
  const end = now.endOf('month').format(format)
  return { start, end }
}

/**
 * 创建日期缓存管理器
 * 用于缓存当前日期，避免频繁计算，支持自动更新
 */
export class DateCache {
  constructor(updateInterval = 60000) {
    // 默认1分钟更新一次
    this._currentDate = null
    this._currentDateTime = null
    this._lastUpdate = 0
    this.updateInterval = updateInterval
    this._updateTimer = null

    this._startAutoUpdate()
  }

  /**
   * 获取缓存的当前日期
   * @returns {string} 当前日期 YYYY-MM-DD 格式
   */
  getCurrentDate() {
    this._checkAndUpdate()
    return this._currentDate
  }

  /**
   * 获取缓存的当前日期时间
   * @returns {string} 当前日期时间 YYYY-MM-DD HH:mm:ss 格式
   */
  getCurrentDateTime() {
    this._checkAndUpdate()
    return this._currentDateTime
  }

  /**
   * 强制更新缓存
   */
  forceUpdate() {
    this._updateCache()
  }

  /**
   * 停止自动更新
   */
  stopAutoUpdate() {
    if (this._updateTimer) {
      clearInterval(this._updateTimer)
      this._updateTimer = null
    }
  }

  /**
   * 启动自动更新
   */
  _startAutoUpdate() {
    this._updateCache()
    this._updateTimer = setInterval(() => {
      this._updateCache()
    }, this.updateInterval)
  }

  /**
   * 检查并更新缓存
   */
  _checkAndUpdate() {
    const now = Date.now()
    if (now - this._lastUpdate > this.updateInterval) {
      this._updateCache()
    }
  }

  /**
   * 更新缓存
   */
  _updateCache() {
    const now = dayjs().tz(DATE_CONFIG.TIMEZONE)
    this._currentDate = now.format(DATE_CONFIG.FORMATS.DATE)
    this._currentDateTime = now.format(DATE_CONFIG.FORMATS.DATETIME)
    this._lastUpdate = Date.now()
  }
}

// 导出配置常量
export const { TIMEZONE, FORMATS } = DATE_CONFIG

// 导出默认的日期缓存实例
export const dateCache = new DateCache()

// 向后兼容的默认导出
export default {
  getCurrentDate,
  getCurrentDateTime,
  getCurrentTime,
  getCurrentTimestamp,
  formatTimestamp,
  formatDate,
  isToday,
  isYesterday,
  getRelativeDate,
  getDaysDifference,
  isValidDateFormat,
  getDateRange,
  getThisWeek,
  getThisMonth,
  DateCache,
  dateCache,
  TIMEZONE,
  FORMATS,
}
