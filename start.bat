@echo off
setlocal enabledelayedexpansion

REM Set working directory to script location
cd /d "%~dp0"

REM Set console encoding to UTF-8
chcp 65001 > nul 2>&1

REM Set window title
title 微信消息监听器 - 批处理版本

echo.
echo ========================================
echo    微信消息监听器 - 批处理版本
echo ========================================
echo.

echo [1/6] 检查Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo X 错误：未检测到 Node.js
    echo 请安装 Node.js 18.0.0 或更高版本
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

REM Get Node.js version and check if it's 18 or higher
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo - Node.js版本: %NODE_VERSION%

REM Extract major version number
for /f "tokens=1 delims=." %%a in ("%NODE_VERSION:v=%") do set MAJOR_VERSION=%%a

if %MAJOR_VERSION% LSS 18 (
    echo X 错误：Node.js 版本过低
    echo 当前版本：%NODE_VERSION%，需要：v18.0.0 或更高版本
    echo 请升级 Node.js：https://nodejs.org/
    pause
    exit /b 1
)
echo - Node.js版本符合要求

echo.
echo [2/6] 检查npm...
where npm >nul 2>&1
if errorlevel 1 (
    echo X 错误：未检测到 npm
    echo 请重新安装 Node.js
    pause
    exit /b 1
)
echo - npm已找到

echo.
echo [3/6] 检查项目文件...
if not exist "package.json" (
    echo X 错误：未找到 package.json
    echo 请在项目根目录中运行此脚本
    pause
    exit /b 1
)
echo - package.json已找到

echo.
echo [4/6] 检查依赖...
if not exist "node_modules" (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo X 错误：依赖安装失败
        pause
        exit /b 1
    )
    echo - 依赖安装完成
) else (
    echo - 依赖已安装
)

echo.
echo [5/6] 检查环境配置...
if not exist ".env" if not exist ".env.production" (
    echo X 错误：缺少环境配置文件
    echo 请根据 .env.example 创建 .env 或 .env.production 文件
    echo 并配置必要参数（服务器地址、认证信息、AI 密钥等）
    pause
    exit /b 1
)
echo - 环境配置文件存在

echo.
echo [6/6] 选择运行模式...
echo 环境检查完成！
echo.

echo 请选择运行模式:
echo 1. 生产模式 (推荐)
echo 2. 开发模式 (调试用)
echo.
set /p mode=请输入选项 (1-2): 

if "%mode%"=="2" (
    echo 正在启动开发模式...
    call npm run dev
) else (
    echo 正在启动生产模式...
    call npm start
)

echo.
echo 应用程序已退出
pause