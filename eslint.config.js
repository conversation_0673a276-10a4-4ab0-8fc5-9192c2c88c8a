import globals from 'globals'
import prettier from 'eslint-config-prettier'

export default [
  {
    ignores: [
      'node_modules/',
      'dist/',
      'build/',
      'logs/',
      '*.log',
      'received_files/',
      '.env',
      '.env.*',
      '*.config.js',
      '*.config.cjs',
      '.cache/',
      'cookie-store.json',
      'coverage/',
      '.nyc_output/',
      '*.min.js',
      '*.bundle.js',
    ],
  },
  {
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        ...globals.node,
      },
    },
    rules: {
      // 错误相关
      'no-console': 'warn',
      'no-debugger': 'warn',
      'no-duplicate-imports': 'error',
      'no-unused-vars': [
        'warn',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],

      // 代码风格
      semi: ['error', 'never'],
      quotes: ['error', 'single'],
      indent: ['error', 2, { SwitchCase: 1 }],
      'comma-dangle': ['error', 'always-multiline'],
      'arrow-parens': ['error', 'as-needed'],
      'max-len': [
        'warn',
        {
          code: 100,
          ignoreComments: true,
          ignoreUrls: true,
          ignoreStrings: true,
          ignoreTemplateLiterals: true,
        },
      ],

      // ES6+
      'arrow-body-style': ['error', 'as-needed'],
      'prefer-const': 'error',
      'prefer-template': 'warn',
      'object-shorthand': 'warn',

      // 最佳实践
      curly: ['error', 'all'],
      eqeqeq: ['error', 'always', { null: 'ignore' }],
      'no-return-await': 'error',
      'require-await': 'warn',
    },
    ...prettier,
  },
]
