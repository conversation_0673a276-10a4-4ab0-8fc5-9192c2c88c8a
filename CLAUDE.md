# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
- `npm run dev` - Start in development mode with nodemon auto-restart
- `npm start` - Start in production mode
- `npm run lint` - Check code style and potential issues
- `npm run lint:fix` - Automatically fix linting issues
- `npm run format` - Format code with Prettier

### Testing
This project does not have automated tests configured. Manual testing is done through the WeChat integration.

### Development Mode
- Development mode uses nodemon with `--inspect` flag for debugging
- Environment automatically loads from `.env.development` in dev mode
- Production mode loads from `.env.production`

## Architecture Overview

This is a WeChat message listener application that processes WeChat messages through AI and forwards structured data to a backend server.

### Core Components

1. **ApplicationManager** (`src/app/applicationManager.js`)
   - Main application orchestrator
   - Initializes WechatferryAgent and manages lifecycle
   - Handles graceful shutdown and error recovery

2. **MessageHandler** (`src/core/messageHandler.js`)
   - Central message processing pipeline
   - Validates contacts, checks keywords, processes different message types
   - Coordinates AI processing and data forwarding

3. **Strategy System** (`src/core/strategies.js`)
   - Determines message processing strategy based on keywords
   - Supports multiple business types (normal, transport, urea, etc.)
   - Uses priority-based keyword matching

4. **AI Service** (`src/services/aiService.js`)
   - Integrates with Moonshot AI for content processing
   - Handles text extraction from images
   - Processes structured data extraction

5. **Data Service** (`src/services/dataService.js`)
   - Manages server data fetching and caching
   - Handles crops, companies, and transport data
   - Provides parallel data loading with error handling

6. **HTTP Client** (`src/services/httpClient.js`)
   - Centralized HTTP communication with backend
   - Handles authentication and session management
   - Includes retry logic and error handling

### Message Flow

1. **Message Reception** - WechatferryAgent receives messages
2. **Contact Validation** - Check if sender is in whitelist (`config/contacts.json`)
3. **Keyword Analysis** - Match content against configured keywords (`config/keywords.json`)
4. **Strategy Selection** - Choose processing strategy based on priority (`config/priorities.json`)
5. **AI Processing** - Extract structured data using appropriate prompt template
6. **Data Matching** - Validate and enrich data using `DataMatcher`
7. **Server Forwarding** - Send processed data to backend server

### Configuration System

All configuration is file-based in the `config/` directory:
- `contacts.json` - Whitelist of allowed contacts and message types
- `keywords.json` - Keywords for different business types
- `priorities.json` - Priority order for keyword matching
- `aiProcessing.json` - AI processing controls per message type
- `aliases.json` - Alias mappings for data standardization
- `prompts/*.md` - AI prompt templates for different message types

### Environment Setup

1. **Prerequisites**:
   - Node.js 18+
   - WeChat client version *********
   - WechatFerry service running on port 10086

2. **Environment Variables** (copy from `.env.example`):
   - Server configuration (URL, auth, timeouts)
   - AI service configuration (Moonshot API key)
   - File storage and logging settings

## Development Guidelines

### Code Style
- ESLint configuration enforces consistent style
- Prettier for code formatting
- No semicolons, single quotes, 2-space indentation
- ES6+ modules with import/export

### Error Handling
- Global error handler in `src/utils/errorHandler.js`
- Structured logging with winston
- Graceful degradation for AI processing failures

### File Structure
- `src/app/` - Application lifecycle management
- `src/core/` - Core business logic
- `src/services/` - External service integrations
- `src/utils/` - Utility functions and helpers
- `config/` - Configuration files
- `logs/` - Application logs
- `received_files/` - Downloaded message attachments

### Adding New Message Types
Use the built-in utility: `node src/utils/addMessageType.js <type> <keywords> <priority> <aiEnabled>`

### Key Dependencies
- `@wechatferry/agent` - WeChat integration
- `axios` - HTTP client with `axios-retry` for resilience
- `openai` - AI service integration (Moonshot AI)
- `winston` - Logging with daily rotation
- `cheerio` - HTML parsing for articles
- `dayjs` - Date handling
- `sharp` - Image processing
- `tough-cookie` - Cookie management for session persistence

### Common Development Tasks
- Monitor logs in `logs/` directory for debugging
- Check `cookie-store.json` for session persistence
- Verify WechatFerry service is running before starting app
- Use development mode for auto-restart during development

### Recent Optimizations
The `src/app/` directory has been optimized for better performance and maintainability:
- Removed hardcoded delays in login handling
- Simplified error handling with unified patterns
- Extracted common utilities to `src/app/utils.js`
- Reduced code complexity and improved readability
- Configuration now passed as single object instead of multiple parameters

### Data Matching System
The `DataMatcher` (`src/utils/dataMatcher.js`) performs:
1. Alias resolution using `config/aliases.json`
2. Fuzzy matching against server data
3. Data enrichment with IDs and standardized names
4. Fallback handling for unmatched data