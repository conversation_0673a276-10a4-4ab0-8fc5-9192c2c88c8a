/**
 * 警告处理模块
 * 用于处理和配置Node.js的警告行为
 */

/**
 * 禁用实验性警告
 * @returns {Function} 恢复原始警告行为的函数
 */
export function disableExperimentalWarnings() {
  return filterWarningsByCode('ExperimentalWarning')
}

/**
 * 创建特定类型的警告过滤器
 * @param {string|Array<string>} codes - 要过滤的警告代码或代码数组
 * @returns {Function} 恢复原始警告行为的函数
 */
export function filterWarningsByCode(codes) {
  // 确保codes是数组
  const warningCodes = Array.isArray(codes) ? codes : [codes]

  // 保存原始的警告函数
  const originalEmitWarning = process.emitWarning

  // 确保process对象存在
  if (!process || typeof process.emitWarning !== 'function') {
    console.warn('警告：无法过滤警告，process.emitWarning不可用')
    return () => {} // 返回空函数
  }

  // 重写警告函数
  process.emitWarning = function (...args) {
    // 检查参数是否存在且包含指定的警告代码
    if (
      args &&
      args.length >= 3 &&
      args[2] &&
      typeof args[2] === 'object' &&
      warningCodes.includes(args[2].code)
    ) {
      return // 忽略指定代码的警告
    }

    // 调用原始警告函数
    return originalEmitWarning.apply(process, args)
  }

  // 返回恢复函数
  return function restoreWarnings() {
    process.emitWarning = originalEmitWarning
  }
}

/**
 * 高级警告过滤器，可以同时基于警告代码和内容进行过滤
 * @param {Object} options - 过滤选项
 * @param {string|Array<string>} [options.codes] - 要过滤的警告代码或代码数组
 * @param {string|Array<string>} [options.contentIncludes] - 警告内容必须包含的字符串或字符串数组
 * @param {Function} [options.customFilter] - 自定义过滤函数，接收完整的警告参数
 * @returns {Function} 恢复原始警告行为的函数
 */
export function createAdvancedWarningFilter({
  codes = [],
  contentIncludes = [],
  customFilter = null,
} = {}) {
  // 标准化参数
  const warningCodes = Array.isArray(codes) ? codes : [codes]
  const contentMatches = Array.isArray(contentIncludes) ? contentIncludes : [contentIncludes]

  // 保存原始的警告函数
  const originalEmitWarning = process.emitWarning

  // 确保process对象存在
  if (!process || typeof process.emitWarning !== 'function') {
    console.warn('警告：无法过滤警告，process.emitWarning不可用')
    return () => {} // 返回空函数
  }

  // 重写警告函数
  process.emitWarning = function (...args) {
    // 检查是否有自定义过滤函数
    if (typeof customFilter === 'function' && customFilter(...args)) {
      return // 自定义过滤函数返回true时忽略警告
    }

    // 代码过滤
    if (
      warningCodes.length > 0 &&
      args.length >= 3 &&
      args[2]?.code &&
      warningCodes.includes(args[2].code)
    ) {
      // 如果没有内容过滤条件，直接忽略
      if (contentMatches.length === 0) {
        return
      }

      // 如果有内容过滤条件，检查是否符合
      const message = typeof args[0] === 'string' ? args[0] : ''
      const matchesContent = contentMatches.some(text => message.includes(text))
      if (matchesContent) {
        return // 忽略指定代码和内容的警告
      }
    }

    // 仅内容过滤（没有代码过滤或代码过滤未触发）
    if (contentMatches.length > 0 && warningCodes.length === 0) {
      const message = typeof args[0] === 'string' ? args[0] : ''
      const matchesContent = contentMatches.some(text => message.includes(text))
      if (matchesContent) {
        return // 忽略包含指定内容的警告
      }
    }

    // 调用原始警告函数
    return originalEmitWarning.apply(process, args)
  }

  // 返回恢复函数
  return function restoreWarnings() {
    process.emitWarning = originalEmitWarning
  }
}
