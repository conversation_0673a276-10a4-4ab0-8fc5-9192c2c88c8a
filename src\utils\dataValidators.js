/**
 * 数据验证模块
 * 提供各种数据类型检查和验证功能
 */

/**
 * 检查数据是否为非空（数组或对象或其他值）
 * @param {any} data 要检查的数据
 * @returns {boolean} 数据是否非空
 */
export function isNonEmptyData(data) {
  if (Array.isArray(data)) {
    return data.length > 0
  }
  if (data && typeof data === 'object') {
    return Object.keys(data).length > 0
  }
  return Boolean(data)
}

/**
 * 检查是否为非空数组
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为非空数组
 */
export function isNonEmptyArray(data) {
  return Array.isArray(data) && data.length > 0
}

/**
 * 检查是否为有效的数组（可以为空）
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为数组
 */
export function isValidArray(data) {
  return Array.isArray(data)
}

/**
 * 检查是否为非空对象
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为非空对象
 */
export function isNonEmptyObject(data) {
  return data && typeof data === 'object' && !Array.isArray(data) && Object.keys(data).length > 0
}

/**
 * 检查是否为空对象
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为空对象
 */
export function isEmptyObject(data) {
  return data && typeof data === 'object' && !Array.isArray(data) && Object.keys(data).length === 0
}

/**
 * 检查是否为空数组
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为空数组
 */
export function isEmptyArray(data) {
  return Array.isArray(data) && data.length === 0
}

/**
 * 检查是否为有效字符串（非空且去除空白后不为空）
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为有效字符串
 */
export function isValidString(data) {
  return typeof data === 'string' && data.trim().length > 0
}

/**
 * 检查是否为有效数字（包括0，但排除NaN和Infinity）
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为有效数字
 */
export function isValidNumber(data) {
  return typeof data === 'number' && !isNaN(data) && isFinite(data)
}

/**
 * 检查是否为正数
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为正数
 */
export function isPositiveNumber(data) {
  return isValidNumber(data) && data > 0
}

/**
 * 检查是否为非负数（包括0）
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为非负数
 */
export function isNonNegativeNumber(data) {
  return isValidNumber(data) && data >= 0
}

/**
 * 检查是否为有效的日期对象
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为有效日期
 */
export function isValidDate(data) {
  return data instanceof Date && !isNaN(data.getTime())
}

/**
 * 检查是否为有效的函数
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为函数
 */
export function isValidFunction(data) {
  return typeof data === 'function'
}

/**
 * 检查是否为null或undefined
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为null或undefined
 */
export function isNullOrUndefined(data) {
  return data === null || data === undefined
}

/**
 * 检查是否为真值（排除null、undefined、false、0、''、NaN）
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为真值
 */
export function isTruthy(data) {
  return Boolean(data)
}

/**
 * 检查是否为假值
 * @param {any} data 要检查的数据
 * @returns {boolean} 是否为假值
 */
export function isFalsy(data) {
  return !Boolean(data)
}
