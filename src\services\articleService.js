/**
 * 微信公众号文章处理服务
 * 集成文章爬取、解析和AI处理功能，支持图片提取和OCR识别
 */

import axios from 'axios'
import * as cheerio from 'cheerio'
import path from 'path'
import fs from 'fs'
import config from '../config/index.js'
import { extractImageWithAi } from './aiService.js'
import { ensureDirSync, fileExistsSync, deleteFile } from '../utils/fileUtils.js'

// 容错和安全配置
const FALLBACK_CONFIG = {
  ENABLE_ARTICLE_WITHOUT_IMAGES: true,
  MAX_IMAGE_FAILURES: 2,
  MAX_CONCURRENT_IMAGES: 3,
}

const SECURITY_CONFIG = {
  ALLOWED_DOMAINS: ['mmbiz.qpic.cn', 'mp.weixin.qq.com', 'mmbiz.qlogo.cn', 'wx.qlogo.cn'],
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  URL_PATTERN: /^https?:\/\//,
}

const CLEANUP_CONFIG = {
  AUTO_CLEANUP_ENABLED: true,
  MAX_STORAGE_SIZE: 100 * 1024 * 1024, // 100MB
  RETENTION_HOURS: 2, // 保留2小时
}

const CONFIG = {
  MAX_RETRIES: 3,
  REQUEST_TIMEOUT: 20000,
  USER_AGENT:
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  SELECTORS: {
    TITLE: '#activity-name',
    AUTHOR: '#js_name',
    DATE: '#publish_time',
    CONTENT: '#js_content',
  },
  ANTI_CRAWL_PATTERNS: /请在微信客户端打开链接|操作频繁|访问受限|请输入验证码/,
  IMAGE_SAVE_DIR: `${config.paths.files}/article_images`,
  MAX_IMAGE_DOWNLOAD_RETRIES: 3,
  IMAGE_TIMEOUT: 15000,
}

const httpClient = axios.create({
  headers: {
    'User-Agent': CONFIG.USER_AGENT,
    Referer: 'https://weixin.sogou.com/',
    'Accept-Language': 'zh-CN,zh;q=0.9',
  },
  timeout: CONFIG.REQUEST_TIMEOUT,
})

const imageHttpClient = axios.create({
  headers: {
    'User-Agent': CONFIG.USER_AGENT,
    Referer: 'https://mp.weixin.qq.com/',
  },
  timeout: CONFIG.IMAGE_TIMEOUT,
  responseType: 'stream',
})

const createResponse = (success, data = null, error = null) => ({ success, data, error })

const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

const exponentialBackoff = attempt => Math.pow(2, attempt - 1) * 1000

// 安全检查函数
function validateImageUrl(imageUrl) {
  if (!imageUrl || typeof imageUrl !== 'string') {
    return { valid: false, reason: '无效的URL格式' }
  }

  if (!SECURITY_CONFIG.URL_PATTERN.test(imageUrl)) {
    return { valid: false, reason: 'URL格式不正确' }
  }

  try {
    const url = new URL(imageUrl)
    const domain = url.hostname

    const isAllowedDomain = SECURITY_CONFIG.ALLOWED_DOMAINS.some(
      allowedDomain => domain === allowedDomain || domain.endsWith('.' + allowedDomain),
    )

    if (!isAllowedDomain) {
      return { valid: false, reason: `不允许的域名: ${domain}` }
    }

    return { valid: true }
  } catch (error) {
    return { valid: false, reason: 'URL解析失败' }
  }
}

function validateImageFile(filePath) {
  try {
    if (!fileExistsSync(filePath)) {
      return { valid: false, reason: '文件不存在' }
    }

    const stats = fs.statSync(filePath)
    if (stats.size > SECURITY_CONFIG.MAX_FILE_SIZE) {
      return { valid: false, reason: `文件过大: ${stats.size} bytes` }
    }

    if (stats.size === 0) {
      return { valid: false, reason: '文件为空' }
    }

    return { valid: true, size: stats.size }
  } catch (error) {
    return { valid: false, reason: `文件检查失败: ${error.message}` }
  }
}

// 资源清理函数
async function cleanupOldFiles() {
  if (!CLEANUP_CONFIG.AUTO_CLEANUP_ENABLED) return

  try {
    if (!fileExistsSync(CONFIG.IMAGE_SAVE_DIR)) return

    const files = fs.readdirSync(CONFIG.IMAGE_SAVE_DIR)
    const now = Date.now()
    const retentionMs = CLEANUP_CONFIG.RETENTION_HOURS * 60 * 60 * 1000
    let cleanedCount = 0

    for (const file of files) {
      const filePath = path.join(CONFIG.IMAGE_SAVE_DIR, file)
      try {
        const stats = fs.statSync(filePath)
        if (now - stats.mtime.getTime() > retentionMs) {
          await deleteFile(filePath)
          cleanedCount++
        }
      } catch (error) {
        log.c.debug(`清理文件失败: ${file}`)
      }
    }

    if (cleanedCount > 0) {
      log.c.info(`清理了 ${cleanedCount} 个过期图片文件`)
    }
  } catch (error) {
    log.c.error(`清理过期文件失败: ${error.message}`)
  }
}

async function checkStorageSpace() {
  try {
    if (!fileExistsSync(CONFIG.IMAGE_SAVE_DIR)) return true

    const files = fs.readdirSync(CONFIG.IMAGE_SAVE_DIR)
    let totalSize = 0

    for (const file of files) {
      const filePath = path.join(CONFIG.IMAGE_SAVE_DIR, file)
      try {
        const stats = fs.statSync(filePath)
        totalSize += stats.size
      } catch (error) {
        // 忽略单个文件错误
      }
    }

    if (totalSize > CLEANUP_CONFIG.MAX_STORAGE_SIZE) {
      log.c.warn(`存储空间超限: ${totalSize} bytes, 开始清理...`)
      await cleanupOldFiles()
      return false
    }

    return true
  } catch (error) {
    log.c.error(`检查存储空间失败: ${error.message}`)
    return true // 检查失败时允许继续
  }
}

function extractArticleUrl(msg) {
  try {
    if (msg.type !== 49 || !msg.content) return null

    const urlMatch = msg.content.match(/<url><!\[CDATA\[(https?:\/\/[^\]]+)\]\]><\/url>/)
    if (urlMatch?.[1]) return urlMatch[1]

    const $ = cheerio.load(msg.content, { xmlMode: true })
    return $('url').text() || null
  } catch (error) {
    log.c.error(`提取文章URL异常: ${error.message}`)
    return null
  }
}

function extractImageUrls(html) {
  const $ = cheerio.load(html)
  const imageUrls = []

  $(CONFIG.SELECTORS.CONTENT)
    .find('img')
    .each((index, element) => {
      const src = $(element).attr('src') || $(element).attr('data-src')
      if (src && src.startsWith('http')) {
        const validation = validateImageUrl(src)
        if (validation.valid) {
          imageUrls.push({
            url: src,
            alt: $(element).attr('alt') || '',
            index: index + 1,
          })
        } else {
          log.c.warn(`跳过不安全的图片URL: ${src}, 原因: ${validation.reason}`)
        }
      }
    })

  return imageUrls
}

async function downloadImage(imageUrl, imageName) {
  try {
    ensureDirSync(CONFIG.IMAGE_SAVE_DIR)

    // 检查存储空间
    await checkStorageSpace()

    const fileName = `${imageName}.jpg`
    const filePath = path.join(CONFIG.IMAGE_SAVE_DIR, fileName)

    if (fileExistsSync(filePath)) {
      const validation = validateImageFile(filePath)
      if (validation.valid) {
        log.c.info(`图片已存在: ${fileName}`)
        return filePath
      } else {
        log.c.warn(`删除无效的已存在文件: ${fileName}`)
        await deleteFile(filePath)
      }
    }

    for (let attempt = 1; attempt <= CONFIG.MAX_IMAGE_DOWNLOAD_RETRIES; attempt++) {
      try {
        const response = await imageHttpClient.get(imageUrl)

        // 检查响应头中的内容类型
        const contentType = response.headers['content-type']
        if (contentType && !SECURITY_CONFIG.ALLOWED_IMAGE_TYPES.includes(contentType)) {
          throw new Error(`不支持的图片类型: ${contentType}`)
        }

        const writer = fs.createWriteStream(filePath)
        response.data.pipe(writer)

        await new Promise((resolve, reject) => {
          writer.on('finish', resolve)
          writer.on('error', reject)
        })

        // 验证下载的文件
        const validation = validateImageFile(filePath)
        if (validation.valid) {
          log.c.success(`图片下载成功: ${fileName} (${validation.size} bytes)`)
          return filePath
        } else {
          await deleteFile(filePath)
          throw new Error(`文件验证失败: ${validation.reason}`)
        }
      } catch (error) {
        log.c.error(`图片下载失败 (第${attempt}次): ${error.message}`)
        if (attempt < CONFIG.MAX_IMAGE_DOWNLOAD_RETRIES) {
          await delay(exponentialBackoff(attempt))
        }
      }
    }

    return null
  } catch (error) {
    log.c.error(`下载图片异常: ${error.message}`)
    return null
  }
}

async function processArticleImages(imageUrls, articleTitle) {
  const imageResults = []
  let failureCount = 0

  // 启动清理任务
  cleanupOldFiles()

  // 分批处理图片以控制并发
  const batchSize = FALLBACK_CONFIG.MAX_CONCURRENT_IMAGES

  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize)

    const batchPromises = batch.map(async imageInfo => {
      try {
        const sanitizedTitle = articleTitle.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '_')
        const imageName = `${sanitizedTitle}_img${imageInfo.index}_${Date.now()}`
        const localPath = await downloadImage(imageInfo.url, imageName)

        if (localPath) {
          let extractedText = ''
          let ocrSuccess = false

          try {
            const result = await extractImageWithAi(localPath)
            if (result?.text) {
              try {
                const parsed = JSON.parse(result.text)
                extractedText = parsed.content || result.text
                ocrSuccess = true
              } catch {
                extractedText = result.text
                ocrSuccess = true
              }
            }
          } catch (error) {
            log.c.warn(`图片OCR处理失败: ${error.message}`)
            failureCount++
          }

          const imageResult = {
            originalUrl: imageInfo.url,
            localPath,
            alt: imageInfo.alt,
            extractedText: extractedText || '',
            index: imageInfo.index,
            ocrSuccess,
          }

          // 如果不需要保留图片文件，处理完后删除
          if (ocrSuccess && !CLEANUP_CONFIG.AUTO_CLEANUP_ENABLED) {
            try {
              await deleteFile(localPath)
              imageResult.localPath = null
            } catch (error) {
              log.c.debug(`删除处理完的图片失败: ${error.message}`)
            }
          }

          return imageResult
        } else {
          failureCount++
          return null
        }
      } catch (error) {
        log.c.error(`处理图片异常: ${error.message}`)
        failureCount++
        return null
      }
    })

    const batchResults = await Promise.allSettled(batchPromises)

    batchResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        imageResults.push(result.value)
      }
    })

    // 检查是否超过最大失败次数
    if (failureCount > FALLBACK_CONFIG.MAX_IMAGE_FAILURES) {
      log.c.warn(`图片处理失败次数过多 (${failureCount}), 启用降级模式`)
      break
    }
  }

  log.c.info(`图片处理完成: 成功${imageResults.length}张, 失败${failureCount}张`)
  return imageResults
}

function parseArticleHtml(html) {
  const $ = cheerio.load(html)

  if (CONFIG.ANTI_CRAWL_PATTERNS.test(html)) {
    return { error: '被微信反爬虫机制拦截，无法获取内容' }
  }

  const title = $(CONFIG.SELECTORS.TITLE).text().trim()
  const author = $(CONFIG.SELECTORS.AUTHOR).text().trim()
  const date = $(CONFIG.SELECTORS.DATE).text().trim()
  const contentElement = $(CONFIG.SELECTORS.CONTENT)

  if (!contentElement.length) {
    return { error: '未找到文章内容元素' }
  }

  const imageUrls = extractImageUrls(html)

  contentElement.find('script, style').remove()
  const content = contentElement.text().trim()

  if (!title || !content) {
    return { error: '文章标题或内容为空，可能不是有效的文章页面' }
  }

  return { title, content, date, author, imageUrls }
}

async function fetchArticleContent(url) {
  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      log.c.info(`开始获取文章内容: ${url} (第${attempt}次尝试)`)

      const response = await httpClient.get(url)
      if (response.status !== 200) {
        log.c.error(`获取文章失败，状态码: ${response.status}`)
        continue
      }

      const articleData = parseArticleHtml(response.data)
      if (articleData.error) {
        return createResponse(false, null, articleData.error)
      }

      log.c.success(`成功获取文章: ${articleData.title}`)
      return createResponse(true, articleData)
    } catch (error) {
      const isLastAttempt = attempt === CONFIG.MAX_RETRIES

      log.c.error(`获取文章内容异常 (第${attempt}次): ${error.message}`, {
        code: error.code,
        status: error.response?.status,
      })

      if (isLastAttempt) {
        return createResponse(
          false,
          null,
          `重试${CONFIG.MAX_RETRIES}次后仍然失败: ${error.message}`,
        )
      }

      await delay(exponentialBackoff(attempt))
    }
  }

  return createResponse(false, null, '获取文章失败，已达最大重试次数')
}

function formatArticleForAi(article, imageResults = []) {
  const { title, author, date, content } = article

  let formattedContent = [
    `标题: ${title || ''}`,
    `作者: ${author || ''}`,
    `发布日期: ${date || ''}`,
    `文章内容:`,
    content || '',
  ].join('\n')

  if (imageResults.length > 0) {
    formattedContent += '\n\n图片内容:'
    imageResults.forEach(image => {
      if (image.extractedText) {
        formattedContent += `\n[图片${image.index}] ${image.extractedText}`
      }
    })
  }

  return formattedContent.trim()
}

async function processArticle(msg) {
  try {
    const articleUrl = extractArticleUrl(msg)
    if (!articleUrl) {
      return createResponse(false, null, '未找到有效的文章URL')
    }

    log.c.info(`开始处理公众号文章: ${articleUrl}`)

    const fetchResult = await fetchArticleContent(articleUrl)
    if (!fetchResult.success) {
      return createResponse(false, null, fetchResult.error || '获取文章内容失败')
    }

    const article = fetchResult.data
    let imageResults = []
    let processingStats = {
      totalImages: 0,
      successImages: 0,
      failedImages: 0,
      fallbackMode: false,
    }

    if (article.imageUrls && article.imageUrls.length > 0) {
      processingStats.totalImages = article.imageUrls.length
      log.c.info(`发现 ${article.imageUrls.length} 张图片，开始处理...`)

      try {
        imageResults = await processArticleImages(article.imageUrls, article.title)
        processingStats.successImages = imageResults.filter(img => img.ocrSuccess).length
        processingStats.failedImages = processingStats.totalImages - imageResults.length

        if (processingStats.failedImages > FALLBACK_CONFIG.MAX_IMAGE_FAILURES) {
          processingStats.fallbackMode = true
          log.c.warn('图片处理失败过多，将仅使用文章文字内容')
        }

        log.c.info(
          `图片处理统计: 总计${processingStats.totalImages}张, 成功${processingStats.successImages}张, 失败${processingStats.failedImages}张`,
        )
      } catch (error) {
        log.c.error(`图片批量处理失败: ${error.message}`)
        if (FALLBACK_CONFIG.ENABLE_ARTICLE_WITHOUT_IMAGES) {
          log.c.info('图片处理失败，继续处理文章文字内容')
          processingStats.fallbackMode = true
        } else {
          return createResponse(false, null, '图片处理失败且未启用无图片模式')
        }
      }
    }

    // 根据容错配置决定是否包含图片内容
    const shouldIncludeImages = !processingStats.fallbackMode && imageResults.length > 0
    const formattedContent = formatArticleForAi(article, shouldIncludeImages ? imageResults : [])

    const result = {
      originalArticle: {
        title: article.title,
        author: article.author,
        date: article.date,
        url: articleUrl,
        content: article.content,
      },
      images: imageResults,
      formattedContent,
      processingStats,
    }

    const successMessage = processingStats.fallbackMode
      ? `公众号文章处理完成(无图片模式): ${article.title}`
      : `公众号文章处理完成: ${article.title}`

    log.c.success(successMessage)
    return createResponse(true, result)
  } catch (error) {
    log.c.error(`处理公众号文章异常: ${error.message}`)
    return createResponse(false, null, error.message)
  }
}

export { processArticle, extractArticleUrl, fetchArticleContent, processArticleImages }
