# 一键启动使用指南

## 文件说明

本项目提供了多种一键启动方案，适用于不同的操作系统和用户需求：

### Windows用户（推荐）

- **`start.bat`** - 完整版启动脚本
- **`start.ps1`** - PowerShell脚本，提供更好的用户体验

### 跨平台用户

- **`launcher.js`** - Node.js启动器，支持所有平台
- **`start.sh`** - Linux/macOS Shell脚本

## 使用方法

### 1. Windows用户 - 根据Node.js管理方式选择

**使用方法：**

1. 双击 `start.bat` 文件
2. 按照提示操作
3. 等待自动启动完成

### 2. Windows PowerShell用户

**使用方法：**

1. 右键点击 `start.ps1` 文件
2. 选择"使用PowerShell运行"
3. 或者在PowerShell中运行：`.\start.ps1`
4. 按照提示操作

### 3. Node.js启动器（所有平台）

```bash
# 生产模式
node launcher.js 1

# 开发模式
node launcher.js 2
```

### 4. Linux/macOS用户

```bash
# 给脚本添加执行权限（首次运行）
chmod +x start.sh

# 运行脚本
./start.sh
```

## 运行模式说明

### 生产模式（推荐）

- 使用 `.env.production` 配置文件
- 适合正式环境使用
- 性能优化，稳定性更好

### 开发模式

- 使用 `.env.development` 配置文件
- 支持热重载，代码修改后自动重启
- 包含调试信息，适合开发调试

## 自动检查项目

启动脚本会自动进行以下检查：

1. **Node.js环境** - 检查是否安装Node.js 18.0.0+
2. **npm工具** - 验证npm是否可用
3. **项目文件** - 确认项目文件完整性
4. **依赖安装** - 自动安装npm依赖（如果未安装）
5. **环境配置** - 检查.env配置文件是否存在
6. **运行模式** - 用户选择运行模式

## 常见问题

### 1. "未找到Node.js"错误

- 下载安装Node.js: https://nodejs.org/
- 建议安装LTS版本（18.0.0或更高）
- 安装后重启命令行窗口

### 2. "缺少环境配置文件"错误

- 复制 `.env.example` 为 `.env` 或 `.env.production`
- 配置必要参数：服务器地址、认证信息、AI密钥等

### 3. "依赖安装失败"错误

- 检查网络连接
- 删除 `node_modules` 文件夹后重试
- 可尝试使用国内镜像：`npm config set registry https://registry.npmmirror.com`

### 4. PowerShell执行策略问题

如果PowerShell脚本无法运行，可能需要修改执行策略：

```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 5. 微信连接问题

- 确保微信客户端版本为3.9.12.17
- 确认WechatFerry服务在端口10086上运行
- 检查防火墙设置

## 项目结构

```
wechat-listener/
├── start.bat              # Windows批处理启动脚本
├── start.ps1              # PowerShell启动脚本
├── launcher.js            # Node.js跨平台启动器
├── start.sh               # Linux/macOS Shell脚本
├── .env.example           # 环境配置示例
├── .env.production        # 生产环境配置
├── .env.development       # 开发环境配置
├── package.json           # 项目依赖配置
└── src/                   # 源代码目录
    └── app/
        └── start.js       # 应用入口文件
```

## 技术支持

如遇问题，请检查：

1. 项目日志文件（`logs/` 目录）
2. 控制台错误输出
3. 环境配置是否正确
4. 网络连接是否正常

---
