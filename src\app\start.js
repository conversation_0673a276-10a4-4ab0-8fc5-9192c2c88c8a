#!/usr/bin/env node

import '../utils/logging.js'

// 内联工具函数
function checkNodeVersion() {
  const requiredVersion = 18
  const currentVersion = parseInt(process.version.slice(1).split('.')[0])

  if (currentVersion < requiredVersion) {
    console.error(`Node.js 版本过低，需要 ${requiredVersion}+ 版本，当前版本: ${process.version}`)
    process.exit(1)
  }
}

function handleError(error, context = '未知') {
  console.error(`[${context}] 发生错误:`, error.message)
  if (error.stack) {
    console.error('错误堆栈:', error.stack)
  }
  process.exit(1)
}

try {
  log.info('🚀 启动微信监听应用...')
  checkNodeVersion()

  import('./main.js').catch(error => handleError(error, '应用启动'))
} catch (error) {
  handleError(error, '启动脚本')
}
