{"semi": false, "trailingComma": "all", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "rangeStart": 0, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.md", "options": {"proseWrap": "always"}}, {"files": ["*.json", "*.jsonc"], "options": {"printWidth": 80}}]}