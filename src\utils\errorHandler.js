/**
 * 错误处理模块
 * 提供全局错误处理功能和统一的异步操作安全包装
 */

import { log } from './logging.js'

/**
 * 安全执行异步操作的通用工具函数
 * @param {Function} asyncFn 要执行的异步函数
 * @param {any} defaultValue 出错时返回的默认值
 * @param {string} context 操作上下文，用于错误日志
 * @param {Object} options 可选配置
 * @param {boolean} options.logError 是否记录错误日志，默认true
 * @param {Function} options.onError 自定义错误处理回调
 * @param {number} options.timeout 超时时间（毫秒）
 * @param {number} options.retries 重试次数，默认0
 * @param {number} options.retryDelay 重试延迟（毫秒），默认1000
 * @param {Function} options.shouldRetry 重试判断函数
 * @returns {Promise<any>} 操作结果或默认值
 */
export async function safeAsync(asyncFn, defaultValue = null, context = '', options = {}) {
  const {
    logError = true,
    onError,
    timeout = null,
    retries = 0,
    retryDelay = 1000,
    shouldRetry = null,
  } = options

  let lastError = null
  let attempt = 0

  while (attempt <= retries) {
    try {
      let operationPromise = asyncFn()

      // 添加超时控制
      if (timeout) {
        operationPromise = Promise.race([
          operationPromise,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error(`操作超时: ${timeout}ms`)), timeout),
          ),
        ])
      }

      const result = await operationPromise
      return result
    } catch (error) {
      lastError = error
      attempt++

      // 检查是否应该重试
      if (attempt <= retries) {
        const shouldRetryOperation = shouldRetry
          ? shouldRetry(error, attempt)
          : _shouldRetryByDefault(error)

        if (shouldRetryOperation) {
          if (logError) {
            log.warn(`${context}执行失败，准备重试 (${attempt}/${retries + 1})`, {
              error: error.message,
              attempt,
              context,
            })
          }

          if (retryDelay > 0) {
            await _delay(retryDelay * attempt) // 指数退避
          }
          continue
        }
      }

      // 记录最终失败
      if (logError && context) {
        log.warn(`${context}: ${error.message}`, {
          error: error.message,
          stack: error.stack,
          context,
          attempts: attempt,
        })
      }

      // 执行自定义错误处理
      if (onError) {
        try {
          onError(error, context)
        } catch (callbackError) {
          log.error('错误处理回调执行失败', {
            callbackError: callbackError.message,
            originalError: error.message,
            context,
          })
        }
      }

      break
    }
  }

  return defaultValue
}

/**
 * 文件操作专用的安全异步执行
 * @param {Function} operation 文件操作函数
 * @param {string} filePath 文件路径
 * @param {any} defaultValue 默认返回值
 * @returns {Promise<any>} 操作结果或默认值
 */
export async function safeFileOperation(operation, filePath, defaultValue = null) {
  return safeAsync(operation, defaultValue, `文件操作[${filePath}]`)
}

/**
 * 网络请求专用的安全异步执行
 * @param {Function} operation 网络操作函数
 * @param {string} endpoint 请求端点
 * @param {any} defaultValue 默认返回值
 * @returns {Promise<any>} 操作结果或默认值
 */
export async function safeNetworkOperation(operation, endpoint, defaultValue = null) {
  return safeAsync(operation, defaultValue, `网络请求[${endpoint}]`, {
    timeout: 30000,
    retries: 3,
    retryDelay: 2000,
    shouldRetry: (error, attempt) => {
      // 网络错误重试策略
      if (
        error.code === 'ECONNRESET' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('network')
      ) {
        return attempt <= 3
      }

      // HTTP状态码重试策略
      if (error.response?.status >= 500) {
        return attempt <= 2
      }

      return false
    },
  })
}

/**
 * JSON解析的安全执行
 * @param {string} jsonString JSON字符串
 * @param {any} defaultValue 解析失败时的默认值
 * @param {string} context 上下文信息
 * @returns {any} 解析结果或默认值
 */
export function safeJsonParse(jsonString, defaultValue = {}, context = 'JSON解析') {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    log.warn(`${context}失败`, { error: error.message })
    return defaultValue
  }
}

/**
 * 错误处理类
 */
export class ErrorHandler {
  /**
   * 注册全局错误处理器
   */
  static register() {
    process.on('uncaughtException', err => {
      log.error('未捕获的异常', {
        error: err.message,
        stack: err.stack,
      })
    })

    process.on('unhandledRejection', reason => {
      const errorMessage = reason instanceof Error ? reason.message : String(reason)
      log.error('未处理的Promise拒绝', {
        error: errorMessage,
        stack: reason instanceof Error ? reason.stack : '无堆栈信息',
      })
    })
  }
}

/**
 * 创建带有默认错误处理的异步装饰器
 * @param {string} context 操作上下文
 * @param {any} defaultValue 默认返回值
 * @returns {Function} 装饰器函数
 */
export function withErrorHandling(context, defaultValue = null) {
  return function (target, propertyName, descriptor) {
    const method = descriptor.value
    descriptor.value = async function (...args) {
      return safeAsync(() => method.apply(this, args), defaultValue, context)
    }
    return descriptor
  }
}

/**
 * 默认重试策略
 * @param {Error} error 错误对象
 * @returns {boolean} 是否应该重试
 */
function _shouldRetryByDefault(error) {
  const message = error.message?.toLowerCase() || ''
  const code = error.code?.toLowerCase() || ''

  // 网络相关错误可以重试
  if (
    code.includes('econnreset') ||
    code.includes('enotfound') ||
    code.includes('etimedout') ||
    message.includes('timeout') ||
    message.includes('network')
  ) {
    return true
  }

  // AI服务相关错误可以重试
  if (message.includes('rate limit') || message.includes('service unavailable')) {
    return true
  }

  return false
}

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 * @returns {Promise<void>}
 */
function _delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
