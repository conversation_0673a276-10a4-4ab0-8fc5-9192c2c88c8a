import config from '../config/index.js'
import { httpClient } from './httpClient.js'
import { dataLoadingRetry } from '../utils/retryManager.js'
import { isNonEmptyArray } from '../utils/dataValidators.js'
import { log } from '../utils/logging.js'

// 数据类型定义
const DATA_TYPES = {
  CROP: 'crop',
  COMPANY: 'company',
  TRANSPORT: 'transport',
}

// 数据获取配置 - 集成HTTP请求逻辑
const DATA_SOURCES = {
  [DATA_TYPES.CROP]: {
    name: '作物',
    url: () => config.api.server.crop,
    params: { ignoreChildren: 1 },
    method: 'post',
    mapper: data =>
      data?.data?.map(item => ({ id: item.id, name: item.name, unit: item.priceUnit })) || [],
  },
  [DATA_TYPES.COMPANY]: {
    name: '公司',
    url: () => config.api.server.company,
    params: {},
    method: 'post',
    mapper: data =>
      data?.data?.list?.map(item => ({ id: item.id, name: item.name, cropId: item.cropId })) || [],
  },
  [DATA_TYPES.TRANSPORT]: {
    name: '厂车',
    url: () => config.api.server.transport,
    params: {},
    method: 'get',
    mapper: data => data?.data?.map(item => ({ id: item.id, name: item.factoryName })) || [],
  },
}

/**
 * 检查数据有效性
 * @param {Array} data 数据数组
 * @returns {boolean} 是否有效
 */
const isValidData = data => isNonEmptyArray(data)

/**
 * 内部数据获取函数
 * @param {Object} source 数据源配置
 * @returns {Promise<Array>} 处理后的数据数组
 */
async function fetchDataFromSource(source) {
  try {
    const url = source.url()
    if (!url) {
      log.warn(`未配置${source.name}URL，跳过获取`)
      return []
    }

    const requestConfig = { method: source.method.toLowerCase() }
    if (source.method.toLowerCase() === 'get') {
      requestConfig.params = source.params
    } else {
      requestConfig.data = source.params
    }

    const { data } = await httpClient(url, requestConfig)

    if (data?.code === 1) {
      return source.mapper(data)
    }

    log.warn(`获取${source.name}数据失败: ${data?.message || '未知错误'}`)
    return []
  } catch (error) {
    log.error(`获取${source.name}数据异常: ${error.message}`)
    return []
  }
}

/**
 * 服务端数据管理器类
 * 实现懒加载和重试机制
 */
class ServerDataManager {
  constructor() {
    this.data = null
    this.isInitialized = false
    this.isLoading = false
    this.loadingPromise = null // 存储正在进行的加载Promise
  }

  /**
   * 获取服务端数据，支持懒加载和重试
   * @param {boolean} forceReload 是否强制重新加载
   * @returns {Promise<Object|null>} 服务器数据对象或null
   */
  async getData(forceReload = false) {
    // 如果已初始化且不强制重载，直接返回缓存数据
    if (this.isInitialized && this.data && !forceReload) {
      return this.data
    }

    // 如果正在加载中，返回现有的Promise
    if (this.isLoading && this.loadingPromise) {
      log.debug('数据正在加载中，等待现有加载完成')
      return this.loadingPromise
    }

    // 防止重复加载
    if (this.isLoading) {
      log.warn('数据正在加载中，跳过重复请求')
      return null
    }

    // 创建并存储加载Promise
    this.loadingPromise = this._performLoad()
    return this.loadingPromise
  }

  /**
   * 执行实际的数据加载
   * @returns {Promise<Object|null>}
   */
  async _performLoad() {
    this.isLoading = true

    try {
      log.info('开始获取服务端数据...')

      // 使用重试管理器执行数据获取
      const results = await dataLoadingRetry(
        () =>
          Promise.allSettled(
            Object.values(DATA_SOURCES).map(source => fetchDataFromSource(source)),
          ),
        {
          onRetry: (error, attempt, maxRetries) => {
            log.warn(`服务端数据获取重试 (${attempt}/${maxRetries}): ${error.message}`)
          },
        },
      )

      // 构建结果对象
      const serverData = {}
      const dataTypes = Object.keys(DATA_SOURCES)
      let successCount = 0

      // 处理每个数据源的结果
      results.forEach((result, index) => {
        const dataType = dataTypes[index]
        const sourceName = DATA_SOURCES[dataType].name
        const data = result.status === 'fulfilled' ? result.value : []

        if (result.status === 'rejected') {
          log.error(`获取${sourceName}数据失败: ${result.reason.message}`)
        }

        // 存储数据（fetchDataFromSource已处理错误情况，返回空数组）
        serverData[`${dataType}Data`] = data
        if (data.length > 0) {
          successCount++
        }
      })

      // 如果所有数据源都失败，抛出错误
      if (successCount === 0) {
        throw new Error('所有数据源获取失败')
      }

      // 构建成功消息
      const successParts = Object.entries(serverData)
        .filter(([_, value]) => value.length > 0)
        .map(([key, value]) => {
          const typeName = key.replace('Data', '')
          return `${value.length} 条${typeName}数据`
        })

      log.success(`数据获取完成：${successParts.join('，')}`)

      // 更新状态
      this.data = serverData
      this.isInitialized = true
      this.isLoading = false
      this.loadingPromise = null

      return serverData
    } catch (error) {
      this.isLoading = false
      this.loadingPromise = null
      log.error(`服务器数据获取失败: ${error.message}`)
      return null
    }
  }

  /**
   * 初始化服务端数据（非阻塞）
   * 用于应用启动时预加载数据
   */
  async initializeAsync() {
    if (this.isInitialized || this.isLoading) {
      log.debug('数据管理器已初始化或正在加载，跳过重复初始化')
      return
    }

    try {
      await this.getData()
    } catch (error) {
      // 初始化失败不影响应用启动
      log.warn(`服务端数据初始化失败: ${error.message}，将在首次使用时重试`)
    }
  }

  /**
   * 重置管理器状态
   */
  reset() {
    this.data = null
    this.isInitialized = false
    this.isLoading = false
    this.loadingPromise = null
  }

  /**
   * 检查数据是否可用
   * @returns {boolean}
   */
  isDataAvailable() {
    return this.isInitialized && this.data !== null
  }
}

// 导出单例实例
export const serverDataManager = new ServerDataManager()
