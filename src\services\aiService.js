import fs from 'fs'
import sharp from 'sharp'
import OpenAI from 'openai'
import config from '../config/index.js'
import { KEYWORD_TYPES } from '../core/strategies.js'
import { buildPrompt as buildPromptFromModule } from '../core/prompts/index.js'
import { deleteFile, isReadable as fileIsReadable } from '../utils/fileUtils.js'
import DataMatcher from '../utils/dataMatcher.js'
import ResultValidator from '../utils/resultValidator.js'
import { cleanTextFormat } from '../utils/messageUtils.js'
import { createAdvancedWarningFilter } from '../utils/nodeWarnings.js'
import { serverDataManager } from './dataService.js'
import { aiApiRetry } from '../utils/retryManager.js'
import { safeAsync } from '../utils/errorHandler.js'
import { isValidArray, isNonEmptyArray } from '../utils/dataValidators.js'
import { getCurrentDate } from '../utils/dateUtils.js'
import { log } from '../utils/logging.js'

// --- 模块常量与配置 ---
const CONFIG = {
  api: {
    model: 'moonshot-v1-auto',
    temperature: 0.3,
    response_format: { type: 'json_object' },
    max_tokens: 30 * 1024,
  },
  image: { maxWidth: 2048, quality: 100 },
  strategies: {
    [KEYWORD_TYPES.TRANSPORT]: { companyField: 'factoryName', dataSource: 'transport' },
    default: { companyField: 'company', dataSource: 'companies' },
  },
}

// --- 验证器 ---
const Validator = {
  initialized: service => {
    if (!service?.initialized || !service.client) throw new Error('AI服务未初始化或客户端无效')
  },
  content: (content, type = '内容') => {
    if (!content || (typeof content === 'string' && !content.trim()))
      throw new Error(`无效的${type}`)
  },
  filePath: filePath => {
    if (!filePath) throw new Error('无效的文件路径')
  },
}

// --- 辅助模块 ---

/**
 * @private
 * @description 封装所有与图片处理相关的逻辑。
 */
const ImageHandler = {
  async _ensureFileClass() {
    if (typeof globalThis.File !== 'undefined') return
    try {
      const restore = createAdvancedWarningFilter({
        codes: 'ExperimentalWarning',
        contentIncludes: 'buffer.File',
      })
      const { File } = await import('node:buffer')
      globalThis.File = File
      restore()
    } catch (err) {
      log.c.error(`动态导入 node:buffer.File 失败：${err.message}`)
    }
  },

  async preprocess(inputPath) {
    const tempPath = `${inputPath}.processed.jpg`
    try {
      const metadata = await sharp(inputPath).metadata()
      let processor = sharp(inputPath)
      if (metadata.width > CONFIG.image.maxWidth) {
        processor = processor.resize({ width: CONFIG.image.maxWidth })
      }
      await processor
        .greyscale()
        .normalize()
        .jpeg({ quality: CONFIG.image.quality })
        .toFile(tempPath)
      return { path: tempPath, cleanup: () => deleteFile(tempPath) }
    } catch (error) {
      log.c.warn(`图片预处理失败: ${error.message}，将使用原图。`)
      return { path: inputPath, cleanup: () => Promise.resolve() }
    }
  },

  async extractText(filePath, client) {
    await this._ensureFileClass()
    if (!(await fileIsReadable(filePath))) throw new Error(`图片文件不可读: ${filePath}`)

    let fileId, stream, prepResult
    try {
      prepResult = await this.preprocess(filePath)
      stream = fs.createReadStream(prepResult.path)
      const file = await aiApiRetry(() =>
        client.files.create({ file: stream, purpose: 'file-extract' }),
      )
      fileId = file.id
      const content = await aiApiRetry(() => client.files.content(fileId).then(res => res.text()))
      return { text: content?.trim() || '', fileId }
    } catch (error) {
      log.c.error(`图片文本提取失败: ${error.message}`)
      if (fileId) return { text: '', fileId, error: error.message }
      throw error
    } finally {
      stream?.destroy()
      await prepResult?.cleanup()
    }
  },

  async cleanup(filePath, fileId, client) {
    if (filePath) {
      await deleteFile(filePath).catch(err => log.c.debug(`清理本地文件失败: ${err.message}`))
    }
    if (fileId && client) {
      await aiApiRetry(() => client.files.delete(fileId)).catch(err =>
        log.c.debug(`清理AI云端文件失败 (${fileId}): ${err.message}`),
      )
    }
  },
}

/**
 * @private
 * @description 封装所有对AI响应的后处理逻辑。
 */
const ResponseHandler = {
  parse(content) {
    if (!content || typeof content !== 'string') return []
    const match = content.trim().match(/(\{[\s\S]*\}|\[[\s\S]*\])/)
    if (!match) return []
    try {
      const parsed = JSON.parse(match[0])
      if (isValidArray(parsed)) return parsed
      const accessors = [
        p => p.results,
        p => p.data?.results,
        p => (Object.values(p).length === 1 ? p[Object.keys(p)[0]] : null),
      ]
      for (const acc of accessors) {
        const data = acc(parsed)
        if (isValidArray(data)) return data
      }
      return Object.values(parsed).flat().filter(Boolean)
    } catch (error) {
      log.c.error(`解析API返回的JSON失败: ${error.message}`)
      return []
    }
  },

  matchAndValidate(apiResults, promptType, serviceState) {
    const { strategy, companyDataSource } = this._getDataSourceConfig(promptType, serviceState)
    const processed = this._processAndMatch(apiResults, strategy, companyDataSource, serviceState)
    const final = this._addDetails(processed, apiResults)
    const validated = ResultValidator.validateAndProcess(final.length ? final : apiResults, {
      promptType,
    })
    return validated.length ? validated : this._createFallback(apiResults)
  },

  _getDataSourceConfig(promptType, { availableTransport, availableCompanies }) {
    const strategy = CONFIG.strategies[promptType] || CONFIG.strategies.default
    const companyDataSource =
      strategy.dataSource === 'transport' ? availableTransport : availableCompanies
    return { strategy, companyDataSource }
  },

  _processAndMatch(apiResults, strategy, companyDataSource, { availableCrops, aliases }) {
    return DataMatcher.processApiResults(apiResults, availableCrops, companyDataSource, {
      companyField: strategy.companyField,
      aliases,
    })
  },

  _addDetails(processedResults, apiResults) {
    if (!processedResults?.length || !apiResults?.length) return processedResults
    const detailsMap = new Map(
      apiResults.map(item => [`${item.company}-${item.crop}-${item.price}`, item.detail]),
    )
    processedResults.forEach(item => {
      const key = `${item.company}-${item.crop}-${item.price}`
      if (detailsMap.has(key)) item.detail = detailsMap.get(key)
    })
    return processedResults
  },

  _createFallback(apiResults) {
    return apiResults.map(item => ({ ...item, _fallback: true, _unverified: true }))
  },
}

/**
 * @class AiService
 * @description 协调AI操作的核心服务，管理状态和流程。
 */
class AiService {
  constructor() {
    this.client = null
    this.initialized = false
    this.prompts = {}
    this.aliases = {}
    this.availableCrops = []
    this.availableCompanies = []
    this.availableTransport = []
  }

  initialize(serverData = {}, prompts = {}, aliases = {}) {
    if (!this._initializeClient()) return false
    this.prompts = prompts || {}
    this.aliases = aliases || {}
    this.updateServerData(serverData)
    this.initialized = true
    log.c.info(`AI服务已初始化，Prompts: ${Object.keys(this.prompts).length}`)
    return true
  }

  _initializeClient() {
    if (this.client) return true
    if (!config.ai.key) {
      log.c.error('AI API Key 未配置')
      return false
    }
    try {
      this.client = new OpenAI({ apiKey: config.ai.key, baseURL: config.ai.baseUrl })
      log.c.success('AI SDK客户端初始化成功')
      return true
    } catch (error) {
      log.c.error(`AI SDK客户端初始化失败: ${error.message}`)
      return false
    }
  }

  updateServerData(serverData = {}) {
    const { cropData = [], companyData = [], transportData = [] } = serverData
    this.availableCrops = isValidArray(cropData) ? cropData : []
    this.availableCompanies = isValidArray(companyData) ? companyData : []
    this.availableTransport = isValidArray(transportData) ? transportData : []
    const total =
      this.availableCrops.length + this.availableCompanies.length + this.availableTransport.length
    if (total > 0)
      log.c.info(
        `AI服务数据已更新: ${this.availableCrops.length} 条作物、${this.availableCompanies.length} 条公司、${this.availableTransport.length} 条厂车数据`,
      )
  }

  async ensureDataAvailable() {
    const total =
      this.availableCrops.length + this.availableCompanies.length + this.availableTransport.length
    if (total > 0) return true
    try {
      const serverData = await serverDataManager.getData()
      if (serverData) {
        this.updateServerData(serverData)
        log.c.info('AI服务已成功懒加载服务器数据。')
        return true
      }
    } catch (error) {
      log.c.warn(`懒加载服务器数据失败: ${error.message}`)
    }
    return false
  }

  async processContent(text, isImage = false, promptType = KEYWORD_TYPES.NORMAL) {
    Validator.initialized(this)
    Validator.content(text, '文本内容')
    await this.ensureDataAvailable()

    const messages = this._buildMessages(text, isImage, promptType)
    const apiContent = await this._callChatCompletion(messages)
    const apiResults = ResponseHandler.parse(apiContent)
    if (!apiResults.length) return []

    return ResponseHandler.matchAndValidate(apiResults, promptType, this)
  }

  async processText(text, promptType = KEYWORD_TYPES.NORMAL) {
    return text ? this.processContent(text, false, promptType) : []
  }

  async processImage(msg = {}, promptType = KEYWORD_TYPES.NORMAL) {
    const { content = '', fileId = '', imagePath } = msg
    if (!imagePath) return []
    try {
      const textToProcess =
        content || (await ImageHandler.extractText(imagePath, this.client))?.text
      if (!textToProcess) return []
      return await this.processContent(textToProcess, true, promptType)
    } finally {
      await ImageHandler.cleanup(imagePath, fileId, this.client)
    }
  }

  async _getTokenCount(messages) {
    if (!this.client || !isNonEmptyArray(messages)) return 0

    try {
      return await aiApiRetry(async () => {
        const response = await this.client.fetch(
          `${config.ai.baseUrl}/tokenizers/estimate-token-count`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${config.ai.key}`,
            },
            body: JSON.stringify({ model: CONFIG.api.model, messages }),
          },
        )

        const data = await response.json()
        return data?.data?.total_tokens || 0
      })
    } catch (error) {
      log.c.error(`计算 token 数量失败: ${error.message}`)
      return 0
    }
  }

  async _callChatCompletion(messages, options = {}) {
    const apiParams = {
      model: CONFIG.api.model,
      messages,
      temperature: CONFIG.api.temperature,
      // response_format: CONFIG.api.response_format,
      // max_tokens: CONFIG.api.max_tokens,
      ...options,
    }
    const completion = await aiApiRetry(() => this.client.chat.completions.create(apiParams))
    let { message, finish_reason } = completion.choices[0]
    let content = message.content
    while (finish_reason === 'length') {
      log.c.info('API响应因长度截断，正在继续获取...')
      const nextCompletion = await aiApiRetry(() =>
        this.client.chat.completions.create({
          ...apiParams,
          messages: [...messages, { role: 'assistant', content, partial: true }],
        }),
      )
      finish_reason = nextCompletion.choices[0].finish_reason
      content += nextCompletion.choices[0].message.content
    }
    if (!content) throw new Error('AI API返回内容为空')
    return content
  }

  _buildMessages(text, isImage, promptType) {
    const cropsInfo = this.availableCrops.map(c => ({ name: c.name, unit: c.priceUnit }))
    const systemPrompt = buildPromptFromModule(
      promptType,
      isImage,
      getCurrentDate(),
      cropsInfo,
      this.prompts,
    )

    const messages = [{ role: 'system', content: systemPrompt }]

    if (isImage) {
      messages.push(
        { role: 'system', content: text },
        { role: 'user', content: '请从上述图片中提取价格信息，仅返回JSON格式数据' },
      )
    } else {
      messages.push({ role: 'user', content: text })
    }

    return messages
  }
}

// --- 单例管理 ---
let aiServiceInstance = null
const getAiService = () => aiServiceInstance

export function initializeAiService(serverData = {}, prompts = {}, aliases = {}) {
  return safeAsync(
    () => {
      if (!aiServiceInstance) {
        aiServiceInstance = new AiService()
        return aiServiceInstance.initialize(serverData, prompts, aliases)
      }
      if (serverData && Object.keys(serverData).length)
        aiServiceInstance.updateServerData(serverData)
      if (prompts && Object.keys(prompts).length) aiServiceInstance.prompts = prompts
      if (aliases && Object.keys(aliases).length) aiServiceInstance.aliases = aliases
      return aiServiceInstance.initialized
    },
    false,
    '初始化AI服务',
  )
}

// --- 通用处理器工厂 ---
const createProcessor =
  processFn =>
  async (input, ...args) => {
    return safeAsync(
      async () => {
        const service = getAiService()
        Validator.initialized(service)
        await service.ensureDataAvailable()
        return await processFn(service, input, ...args)
      },
      [],
      'AI处理',
    )
  }

// --- 导出的公共接口 ---
export const processTextWithAi = createProcessor((service, content, promptType) => {
  Validator.content(content, '文本内容')
  return service.processText(cleanTextFormat(content), promptType)
})

export const processImageWithAi = createProcessor((service, msg, promptType) => {
  Validator.filePath(msg?.imagePath)
  if (msg?.content) msg.content = cleanTextFormat(msg.content)
  return service.processImage(msg, promptType)
})

export const extractImageWithAi = createProcessor(async (service, imagePath) => {
  Validator.filePath(imagePath)
  const result = await ImageHandler.extractText(imagePath, service.client)
  if (result?.text) result.text = cleanTextFormat(result.text)
  return result
})
