import { setTimeout } from 'timers/promises'
import config from '../config/index.js'
import { ensureDirSync, fileExistsSync } from '../utils/fileUtils.js'

// 常量配置
const MAX_DOWNLOAD_RETRIES = 3
const MAX_DECRYPT_ATTEMPTS = 10
const RETRY_DELAY_MS = 1500 // 增加重试间隔
const DECRYPT_TIMEOUT_MS = 30000 // 添加解密超时时间
const DEFAULT_SAVE_DIR = config.paths.files

/**
 * 检查并创建保存目录
 * @param {string} saveDir
 */
function ensureSaveDir(saveDir) {
  if (!saveDir || typeof saveDir !== 'string') {
    throw new Error('无效的图片保存目录')
  }
  ensureDirSync(saveDir)
}

/**
 * 自定义图片下载函数，解决WechatFerry图片下载失败问题
 * @param {Object} agent WechatferryAgent实例
 * @param {Object} message 消息对象
 * @param {string} saveDir 保存目录
 * @returns {Promise<string|null>} 保存的文件路径或null
 */
async function downloadImage(agent, message, saveDir = DEFAULT_SAVE_DIR) {
  try {
    ensureSaveDir(saveDir)
    if (
      !agent ||
      !agent.wcf ||
      typeof agent.wcf.downloadAttach !== 'function' ||
      typeof agent.wcf.decryptImage !== 'function'
    ) {
      log.c.error('agent.wcf.downloadAttach 或 decryptImage 方法不存在')
      return null
    }
    const { id, thumb, extra } = message
    log.c.info(`开始下载图片，消息ID: ${id}`)
    let downloadResult = -1
    for (let retryAttempt = 0; retryAttempt < MAX_DOWNLOAD_RETRIES; retryAttempt++) {
      try {
        downloadResult = agent.wcf.downloadAttach(id, thumb, extra)
        if (downloadResult === 0) {
          log.c.info('图片下载请求成功，等待下载完成...')
          break
        } else {
          log.c.warn(
            `下载图片失败(${retryAttempt + 1}/${MAX_DOWNLOAD_RETRIES})，状态码: ${downloadResult}`,
          )
        }
      } catch (err) {
        log.c.warn(`下载图片异常(${retryAttempt + 1}/${MAX_DOWNLOAD_RETRIES}): ${err.message}`)
      }
      if (retryAttempt < MAX_DOWNLOAD_RETRIES - 1) {
        await setTimeout(RETRY_DELAY_MS)
      }
    }
    if (downloadResult !== 0) {
      log.c.error(`图片未能下载，已重试${MAX_DOWNLOAD_RETRIES}次`)
      return null
    }
    // 等待图片下载完成并解密
    let decryptedPath = null
    const startTime = Date.now()

    for (let decryptAttempt = 0; decryptAttempt < MAX_DECRYPT_ATTEMPTS; decryptAttempt++) {
      try {
        // 检查是否超过总超时时间
        if (Date.now() - startTime > DECRYPT_TIMEOUT_MS) {
          log.c.error('图片解密总超时，停止重试')
          break
        }

        decryptedPath = agent.wcf.decryptImage(extra || '', saveDir)
        log.c.info(
          `尝试解密图片(${decryptAttempt + 1}/${MAX_DECRYPT_ATTEMPTS}): ${decryptedPath || '未返回路径'}`,
        )
        if (decryptedPath && fileExistsSync(decryptedPath)) {
          log.c.success(`图片已保存到: ${decryptedPath}`)
          return decryptedPath
        }
      } catch (err) {
        log.c.warn(`解密图片异常(${decryptAttempt + 1}/${MAX_DECRYPT_ATTEMPTS})`, {
          error: err.message,
          stack: err.stack,
        })

        // 如果是RPC相关错误，建议重启微信客户端
        if (err.message.includes('rpc') || err.message.includes('Timed out')) {
          log.c.error('检测到RPC通信异常，建议检查微信客户端状态')
        }
      }

      if (decryptAttempt < MAX_DECRYPT_ATTEMPTS - 1) {
        // 动态调整重试间隔，逐渐增加
        const dynamicDelay = RETRY_DELAY_MS * (1 + decryptAttempt * 0.1)
        await setTimeout(Math.min(dynamicDelay, 5000))
      }
    }

    log.c.error(
      `图片解密失败，已重试${MAX_DECRYPT_ATTEMPTS}次，总耗时: ${Date.now() - startTime}ms`,
    )
    return null
  } catch (error) {
    log.c.error(`图片下载处理异常: ${error.message}`)
    return null
  }
}

export { downloadImage }
