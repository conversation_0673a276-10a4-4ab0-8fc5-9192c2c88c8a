/**
 * 统一重试管理器
 * 提供可配置的重试策略和错误处理
 */

// 重试策略枚举
export const RETRY_STRATEGIES = {
  FIXED: 'fixed', // 固定延迟
  LINEAR: 'linear', // 线性递增
  EXPONENTIAL: 'exponential', // 指数退避
}

// 默认配置
const DEFAULT_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  strategy: RETRY_STRATEGIES.LINEAR,
  maxDelay: 30000,
  factor: 2,
  jitter: true,
  onRetry: null,
  shouldRetry: null,
}

/**
 * 通用重试管理器类
 */
export class RetryManager {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.validateConfig()
  }

  /**
   * 验证配置参数
   */
  validateConfig() {
    const { maxRetries, baseDelay, maxDelay, factor } = this.config

    if (maxRetries < 0) throw new Error('maxRetries必须大于等于0')
    if (baseDelay <= 0) throw new Error('baseDelay必须大于0')
    if (maxDelay < baseDelay) throw new Error('maxDelay必须大于等于baseDelay')
    if (factor <= 1) throw new Error('factor必须大于1')
  }

  /**
   * 计算延迟时间
   * @param {number} attempt 当前重试次数 (0-based)
   * @returns {number} 延迟毫秒数
   */
  calculateDelay(attempt) {
    const { strategy, baseDelay, factor, maxDelay, jitter } = this.config
    let delay

    switch (strategy) {
      case RETRY_STRATEGIES.FIXED:
        delay = baseDelay
        break
      case RETRY_STRATEGIES.LINEAR:
        delay = baseDelay + attempt * baseDelay
        break
      case RETRY_STRATEGIES.EXPONENTIAL:
        delay = baseDelay * Math.pow(factor, attempt)
        break
      default:
        delay = baseDelay
    }

    // 应用最大延迟限制
    delay = Math.min(delay, maxDelay)

    // 添加抖动以避免雷鸣群效应
    if (jitter) {
      delay = delay * (0.5 + Math.random() * 0.5)
    }

    return Math.floor(delay)
  }

  /**
   * 默认重试条件判断
   * @param {Error} error 错误对象
   * @param {number} attempt 当前重试次数
   * @returns {boolean} 是否应该重试
   */
  defaultShouldRetry(error, attempt) {
    // 网络相关错误
    const networkErrors = [
      'ECONNREFUSED',
      'ECONNRESET',
      'ETIMEDOUT',
      'ECONNABORTED',
      'ENETDOWN',
      'ENETUNREACH',
      'EHOSTDOWN',
      'EHOSTUNREACH',
    ]

    if (networkErrors.includes(error.code)) return true

    // HTTP 状态码
    if (error.response?.status >= 500) return true
    if (error.response?.status === 429) return true // Rate limit

    // 超时错误
    if (/timeout|network error/i.test(error.message)) return true

    return false
  }

  /**
   * 执行带重试的操作
   * @param {Function} operation 要执行的异步操作
   * @param {Object} options 额外配置选项
   * @returns {Promise} 操作结果
   */
  async execute(operation, options = {}) {
    const config = { ...this.config, ...options }
    const { maxRetries, onRetry, shouldRetry } = config

    let lastError
    let attempt = 0

    while (attempt <= maxRetries) {
      try {
        // 第一次尝试不延迟，后续重试才延迟
        if (attempt > 0) {
          const delay = this.calculateDelay(attempt - 1)
          await this.sleep(delay)
        }

        const result = await operation()
        return result
      } catch (error) {
        lastError = error

        // 检查是否应该重试
        const shouldRetryFn = shouldRetry || this.defaultShouldRetry.bind(this)
        const canRetry = attempt < maxRetries && shouldRetryFn(error, attempt)

        if (!canRetry) {
          break
        }

        // 调用重试回调
        if (onRetry) {
          try {
            await onRetry(error, attempt + 1, maxRetries)
          } catch (callbackError) {
            log.warn(`重试回调执行失败: ${callbackError.message}`)
          }
        }

        attempt++
      }
    }

    // 所有重试都失败了，抛出最后的错误
    throw new RetryExhaustedError(lastError, attempt, maxRetries)
  }

  /**
   * 延迟执行
   * @param {number} ms 延迟毫秒数
   * @returns {Promise}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 创建预配置的重试器
   * @param {Object} config 配置对象
   * @returns {Function} 重试执行函数
   */
  static create(config = {}) {
    const manager = new RetryManager(config)
    return (operation, options) => manager.execute(operation, options)
  }
}

/**
 * 重试耗尽错误类
 */
export class RetryExhaustedError extends Error {
  constructor(originalError, attemptCount, maxRetries) {
    super(`重试${attemptCount}次后仍然失败 (最大重试次数: ${maxRetries}): ${originalError.message}`)
    this.name = 'RetryExhaustedError'
    this.originalError = originalError
    this.attemptCount = attemptCount
    this.maxRetries = maxRetries
  }
}

// 预定义的重试器配置
export const RETRY_PRESETS = {
  // 网络请求重试
  NETWORK: {
    maxRetries: 3,
    baseDelay: 1000,
    strategy: RETRY_STRATEGIES.EXPONENTIAL,
    factor: 2,
    maxDelay: 10000,
    shouldRetry: error => {
      const networkCodes = ['ECONNREFUSED', 'ETIMEDOUT', 'ECONNABORTED']
      return (
        networkCodes.includes(error.code) ||
        error.response?.status >= 500 ||
        /network error|timeout/i.test(error.message)
      )
    },
  },

  // AI API重试
  AI_API: {
    maxRetries: 2,
    baseDelay: 1000,
    strategy: RETRY_STRATEGIES.LINEAR,
    shouldRetry: error => {
      // 网络错误
      const networkCodes = ['ECONNREFUSED', 'ETIMEDOUT', 'ECONNABORTED']
      if (networkCodes.includes(error.code)) return true

      // HTTP状态码错误
      if (error.response?.status >= 500) return true
      if (error.response?.status === 429) return true // Rate limit

      // 关键词匹配
      if (/rate limit|timeout|network|模拟|持续失败/i.test(error.message)) return true

      return false
    },
  },

  // 数据加载重试 (有冷却期)
  DATA_LOADING: {
    maxRetries: 3,
    baseDelay: 5000,
    strategy: RETRY_STRATEGIES.FIXED,
    shouldRetry: error => {
      return /ECONNREFUSED|timeout|network/i.test(error.message)
    },
  },

  // 快速重试（用于轻量级操作）
  FAST: {
    maxRetries: 2,
    baseDelay: 500,
    strategy: RETRY_STRATEGIES.LINEAR,
    maxDelay: 2000,
  },
}

// 导出预配置的重试器
export const networkRetry = RetryManager.create(RETRY_PRESETS.NETWORK)
export const aiApiRetry = RetryManager.create(RETRY_PRESETS.AI_API)
export const dataLoadingRetry = RetryManager.create(RETRY_PRESETS.DATA_LOADING)
export const fastRetry = RetryManager.create(RETRY_PRESETS.FAST)

// 默认重试器
export const defaultRetry = RetryManager.create()
