import { WechatferryAgent } from '@wechatferry/agent'
import handleMessage from '../core/messageHandler.js'
import config from '../config/index.js'
import { initializeAiService } from '../services/aiService.js'
import { authManager } from '../services/httpClient.js'
import { serverDataManager } from '../services/dataService.js'
import { log } from '../utils/logging.js'

const SHUTDOWN_TIMEOUT = 5000

function createTimeoutPromise(ms = SHUTDOWN_TIMEOUT) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

function logContactInfo(contacts, rooms) {
  const contactCount = contacts?.length || 0
  const roomCount = rooms?.length || 0
  log.c.success(`✅ 已加载 ${contactCount} 个联系人，${roomCount || '--'} 个群聊`)
}

function logAiStatus(aiProcessingConfig) {
  if (!aiProcessingConfig) return

  const status = aiProcessingConfig.enabled ? '✅ 已启用' : '❌ 已禁用'
  log.c.success(`AI处理状态: ${status}`)

  if (aiProcessingConfig.typeSettings) {
    const types = Object.entries(aiProcessingConfig.typeSettings)
      .map(([type, enabled]) => `${type}: ${enabled ? '开启' : '关闭'}`)
      .join(', ')
    log.c.info(`分类状态: ${types}`)
  }
}

function showWelcome() {
  log.c.warn('='.repeat(50))
  log.c.info('🚀 微信消息监听应用已启动')
  log.c.info('📱 基于 @wechatferry/agent 实现')
  log.c.info('⚠️  请确保微信客户端(*********版本)已登录')
  log.c.info('💡 按 Ctrl+C 退出程序')
  log.c.warn('='.repeat(50))
}

export class ApplicationManager {
  constructor(configs) {
    this.agent = null
    this.shutdownInProgress = false
    this.configs = configs
  }

  async gracefulShutdown(signal = 'SIGINT') {
    if (this.shutdownInProgress) return
    this.shutdownInProgress = true

    log.c.warn(`⚠️  收到${signal}信号，正在关闭应用...`)

    try {
      await Promise.race([this.agent?.stop?.(), createTimeoutPromise()])
    } catch (err) {
      log.c.error(`❌ 应用关闭失败: ${err.message}`)
    }

    log.c.success('✅ 应用已安全关闭')
    process.exit(0)
  }

  async handleLogin(user) {
    log.success(`✅ 微信已登录: ${user.name} (${user.wxid})`)

    try {
      const [contacts, rooms] = await Promise.all([
        this.agent.getContactList(),
        this.agent.getChatRoomList(),
      ])

      logContactInfo(contacts, rooms)
    } catch (err) {
      log.error(`❌ 获取联系人失败: ${err.message}`)
    }
  }

  handleLogout() {
    log.info('📱 微信已退出登录')
  }

  /**
   * 统一初始化服务器和AI服务 - 解耦以提高容错性
   */
  async initializeServerAndAI() {
    let serverData = null
    try {
      // 1. 尝试初始化服务器连接并加载数据
      await this.initializeServerConnection()
      serverData = await this.loadServerData()
      log.success('🎉 服务器数据加载完成')
    } catch (error) {
      log.warn(`🔌 服务器连接失败: ${error.message} - AI服务将以降级模式运行`)
    }

    try {
      // 2. 无论服务器状态如何，都初始化AI服务
      // serverData 可能为 null，setupAiService 需要能处理这种情况
      await this.setupAiService(serverData)
    } catch (error) {
      // 这个 catch 主要用于捕获 setupAiService 内部的非预期错误
      log.error(`❌ AI服务设置失败: ${error.message}`)
    }
  }

  /**
   * 初始化服务器连接
   */
  async initializeServerConnection() {
    if (!authManager.hasValidSession(config.server.url)) {
      log.info('🔄 会话过期，正在重新登录服务器...')
      if (!(await authManager.login())) {
        throw new Error('服务器登录失败')
      }
      log.success('✅ 服务器登录成功')
    }
  }

  /**
   * 加载服务器数据
   * @returns {Promise<Object|null>} 服务器数据或null
   */
  async loadServerData() {
    try {
      await serverDataManager.initializeAsync()
      const data = await serverDataManager.getData()
      if (data) {
        log.success('✅ 服务器数据加载成功')
        return data
      } else {
        log.warn('⚠️  服务器数据加载失败，将使用降级模式')
        return null
      }
    } catch (error) {
      log.warn(`⚠️  服务器数据加载异常: ${error.message}，将使用降级模式`)
      return null
    }
  }

  /**
   * 初始化AI服务
   * @param {Object|null} serverData 服务器数据对象
   */
  async setupAiService(serverData = null) {
    try {
      // AI服务支持智能更新，无需重复初始化
      const dataToUse = serverData || {}
      const success = await initializeAiService(
        dataToUse,
        this.configs.prompts,
        this.configs.aliases,
      )

      if (!success) {
        log.error('❌ AI服务初始化失败')
        return
      }

      // 记录初始化状态
      if (serverData) {
        logAiStatus(this.configs.aiProcessingConfig)
        log.success('🤖 AI服务已使用完整服务端数据初始化')
      } else {
        log.info('⚡ AI服务已使用基础配置初始化，服务端数据将在可用时自动更新')
      }
    } catch (error) {
      log.error(`❌ AI服务初始化失败: ${error.message}`)
    }
  }

  setupEventListeners() {
    this.agent.on('login', user => this.handleLogin(user))
    this.agent.on('message', msg => handleMessage(msg, this.agent, this.configs))
    this.agent.on('error', err => log.error(`❌ 微信代理错误: ${err.message}`))
    this.agent.on('logout', () => this.handleLogout())

    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'))
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'))
  }

  async start() {
    try {
      this.agent = new WechatferryAgent()
      this.setupEventListeners()
      await this.agent.start()
      log.info('🚀 应用启动成功')
      // 初始化服务器数据和AI服务
      await this.initializeServerAndAI()
      showWelcome()
    } catch (error) {
      log.error(`❌ 应用启动失败: ${error.message}`)
      await this.cleanup()

      // 避免死循环：如果是服务器连接问题，不要退出进程
      if (error.message.includes('服务器连接') || error.message.includes('ECONNREFUSED')) {
        log.warn('⚠️  检测到服务器连接问题，应用将继续运行但功能受限')
        return
      }

      process.exit(1)
    }
  }

  async cleanup() {
    try {
      await this.agent?.stop?.()
    } catch (error) {
      log.c.error(`❌ 清理失败: ${error.message}`)
    }
  }
}
