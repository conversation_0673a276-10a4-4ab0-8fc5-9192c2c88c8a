# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
max_line_length = 100

# JavaScript/TypeScript files
[*.{js,jsx,ts,tsx,mjs,cjs}]
quote_type = single
curly_bracket_next_line = false
spaces_around_operators = true
spaces_around_brackets = false

# JSON files
[*.json]
insert_final_newline = false

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Shell scripts
[*.sh]
end_of_line = lf

# Windows scripts
[*.{cmd,bat}]
end_of_line = crlf

# Minified files
[*.min.*]
indent_style = ignore
insert_final_newline = ignore
