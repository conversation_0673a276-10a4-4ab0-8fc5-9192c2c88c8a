# 对话规则

```
[[calls]]
match = "when the user asks about code, configuration, debugging, or technical implementation"
tool  = "context7"
```

## 基本设置

- 默认中文对话
- 简洁直接回答
- 专注实际需求

## 任务管理

- 使用 TodoWrite 跟踪进度
- 并行执行独立任务
- 实时更新任务状态
- 完成即标记

## 代码原则

- 保持功能不变
- 遵循DRY原则
- 降低心智负担
- 优先重构现有代码
- 避免创建新文件

## 性能优化

- 测量前后差异
- 关注启动性能
- 优化资源消耗
- 改进容错机制

## 开发偏好

- 优先现代JavaScript语法(ES6+)
- 函数式编程风格
- 异步处理用async/await
- 避免回调地狱
- 使用解构赋值和展开运算符

## 架构思维

- 模块化设计
- 关注点分离
- 依赖注入
- 接口抽象
- 可测试性

## 调试与测试

- 提供调试建议
- 关注错误边界
- 考虑测试覆盖率
- 日志记录策略

## 技术栈理解

- 熟悉前端框架差异
- 了解后端API设计
- 数据库设计考虑
- 部署与运维知识

## 问题解决

- 分析根本原因
- 提供多种解决方案
- 权衡利弊
- 考虑长期维护性
