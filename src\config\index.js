/**
 * 统一配置管理器，提供配置加载和验证功能。
 * 该模块负责加载和验证配置，并提供访问配置的接口。
 */

import path from 'path'

// 基础配置对象，包含所有配置项
const config = {
  // 服务器配置
  server: {
    url: process.env.SERVER_URL,
    timeout: parseInt(process.env.SERVER_TIMEOUT || '5000', 10),
    auth: {
      username: process.env.SERVER_USERNAME,
      password: process.env.SERVER_PASSWORD,
      loginUrl: process.env.SERVER_LOGIN_URL,
    },
  },

  // API 端点
  api: {
    server: {
      message: process.env.SERVER_WECHAT_MSG_URL,
      crop: process.env.SERVER_WECHAT_CROP_URL,
      company: process.env.SERVER_WECHAT_COMPANY_URL,
      transport: process.env.SERVER_WECHAT_TRANSPORT_URL,
    },
  },

  // 文件和日志配置
  paths: {
    files: process.env.FILE_SAVE_DIR || path.join(process.cwd(), 'received_files'),
    logs: process.env.LOG_DIR || path.join(process.cwd(), 'logs'),
    cookies: process.env.COOKIES_FILE || path.join(process.cwd(), './cookie-store.json'),
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
  },

  // AI 配置
  ai: {
    key: process.env.MOONSHOT_API_KEY,
    baseUrl: process.env.MOONSHOT_BASE_URL,
  },
}

/**
 * 验证必要的环境变量
 * @param {Array<string>} required 必需的环境变量列表
 * @throws 如果缺少环境变量则抛出错误
 */
export function validateEnv(
  required = [
    'SERVER_URL',
    'SERVER_USERNAME',
    'SERVER_PASSWORD',
    'SERVER_LOGIN_URL',
    'SERVER_WECHAT_MSG_URL',
    'SERVER_WECHAT_CROP_URL',
    'SERVER_WECHAT_COMPANY_URL',
    'SERVER_WECHAT_TRANSPORT_URL',
    'MOONSHOT_BASE_URL',
    'MOONSHOT_API_KEY',
  ],
) {
  const missing = required.filter(k => !process.env[k])
  if (missing.length > 0) {
    throw new Error(`缺少必要的环境变量: ${missing.join(', ')}`)
  }
}

export default config
