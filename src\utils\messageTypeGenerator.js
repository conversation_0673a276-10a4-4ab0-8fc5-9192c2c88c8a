/**
 * 新消息类型添加工具
 *
 * 使用命令行快速添加新的消息类型
 *
 * 用法:
 * node src/utils/messageTypeGenerator.js <类型名称> [关键词1,关键词2,...] [优先级] [是否启用AI]
 *
 * 例如:
 * node src/utils/messageTypeGenerator.js price "价格,报价,行情" 30 true
 */

import { createMessageTypeConfig } from '../core/configLoader.js'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import fs from 'fs'
import { log } from './logging.js'

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const promptsDir = join(__dirname, '../../config/prompts')

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2)

  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showHelp()
    return
  }

  const typeName = args[0]
  const keywords = args[1] ? args[1].split(',').map(k => k.trim()) : []
  const priority = args[2] ? parseInt(args[2], 10) : 50
  const enableAi = args[3] ? args[3].toLowerCase() === 'true' : true

  log.c.info(`准备添加 ${typeName} 消息类型...`)

  // 创建默认提示词模板
  const promptTemplate = await createDefaultPromptTemplate(typeName)

  try {
    const result = await createMessageTypeConfig(typeName, {
      keywords,
      promptTemplate,
      enableAi,
      priority,
    })

    if (result) {
      log.c.info(`\n✅ 消息类型 ${typeName} 添加成功！`)

      // 显示后续步骤建议
      showNextSteps(typeName)
    } else {
      log.c.error(`\n❌ 消息类型 ${typeName} 添加失败！`)
    }
  } catch (error) {
    log.c.error(`\n❌ 错误: ${error.message}`)
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log.c.info(`
新消息类型添加工具
==================

用法:
  node src/utils/messageTypeGenerator.js <类型名称> [关键词1,关键词2,...] [优先级] [是否启用AI]

参数:
  类型名称    - 必填，新消息类型的名称，如 price, news 等
  关键词     - 可选，触发该类型的关键词，用逗号分隔
  优先级     - 可选，数字越小优先级越高，默认为50
  是否启用AI - 可选，true或false，默认为true

例如:
  node src/utils/messageTypeGenerator.js price "价格,报价,行情" 30 true
`)
}

/**
 * 创建默认提示词模板
 */
async function createDefaultPromptTemplate(typeName) {
  const normalizedType = typeName.toLowerCase()

  // 根据类型名称猜测可能的用途，提供更具体的模板示例
  let outputFormat = ''
  let rules = ''

  switch (normalizedType) {
    case 'price':
    case 'quotation':
      outputFormat = `[
  {
    "company": "公司名称",
    "product": "产品名称",
    "price": "价格",
    "unit": "单位",
    "date": "报价日期"
  }
]`
      rules = `1. 提取消息中提及的所有价格信息
2. 确保每个公司只生成一条记录
3. 日期格式统一为YYYY-MM-DD`
      break

    case 'news':
    case 'announcement':
      outputFormat = `[
  {
    "title": "标题",
    "content": "内容摘要",
    "source": "来源",
    "date": "日期"
  }
]`
      rules = `1. 提取消息中的新闻标题、内容摘要和来源
2. 摘要应该简洁明了，不超过100字
3. 保持客观，不添加个人评价`
      break

    case 'inventory':
    case 'stock':
      outputFormat = `[
  {
    "company": "公司名称",
    "product": "产品名称",
    "quantity": "库存量",
    "unit": "单位",
    "location": "库存地点"
  }
]`
      rules = `1. 提取消息中提及的所有库存信息
2. 确保数量和单位格式统一
3. 每个产品只生成一条记录`
      break

    default:
      outputFormat = `[
  {
    "key": "value",
    "key2": "value2"
  }
]`
      rules = `1. 保持客观，只提取文本中明确提及的信息
2. 保持输出格式一致`
      break
  }

  return `# ${normalizedType.toUpperCase()} 消息处理提示词

## 核心目标
从${normalizedType}消息中提取关键信息并生成结构化数据。

## 输出格式
请以JSON格式输出提取的信息:

\`\`\`json
${outputFormat}
\`\`\`

## 重要规则
${rules}
4. 请严格按照以上规则处理输入文本，仅输出JSON数组，不要添加任何解释性文字
`
}

/**
 * 显示后续步骤建议
 */
function showNextSteps(typeName) {
  const normalizedType = typeName.toLowerCase()
  const promptFileName = `${normalizedType}_prompt.txt`
  const promptFilePath = join(promptsDir, promptFileName)

  log.c.info(`
🔍 后续步骤:

1. 编辑提示词文件, 完善提取规则:
   ${promptFilePath}

2. 测试新消息类型:
   - 发送包含配置关键词的消息
   - 检查消息是否按照预期格式被处理

3. 需要进一步调整时:
   - 修改关键词: 编辑 config/keywords.json
   - 调整优先级: 编辑 config/priorities.json
   - 修改AI处理设置: 编辑 config/aiProcessing.json
`)
}

// 执行主函数
main().catch(console.error)
