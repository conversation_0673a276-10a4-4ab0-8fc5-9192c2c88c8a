# 微信消息监听与智能处理应用

基于 [@wechatferry/agent](https://github.com/wechatferry/wechatferry)
实现的微信消息监听应用，可以实时监听并处理微信消息，结合AI服务提取结构化数据，并通过数据匹配与验证后，将结果转发到指定服务器。

## 核心功能

- **实时消息监听**：支持监听私聊和群聊的文本、图片、公众号文章等多种消息类型。
- **智能AI处理**：集成 [Moonshot AI](https://platform.moonshot.cn/)
  对消息内容进行处理，提取关键信息并生成结构化数据。
- **灵活关键词策略**：支持多种关键词策略（如普通、厂车、尿素等），并可根据优先级进行处理。
- **动态配置**：无需修改代码即可通过配置文件或命令行工具动态添加新的消息类型、关键词和提示词。
- **数据智能匹配**：自动从服务器获取业务数据，并使用 `DataMatcher`
  对AI提取结果进行别名转换和智能模糊匹配，确保数据准确性。
- **公众号文章处理**：内置文章处理服务，可自动抓取公众号文章内容并进行摘要和信息提取。
- **自动文件处理**：自动下载图片消息，提取图片中的文字，并与文本消息统一处理。
- **统一日志系统**：通过 `logUtils.js` 封装，提供统一的日志接口，结合了用于彩色控制台输出的 `log.js`
  和用于文件归档的 `log.js` (winston)。
- **健壮的错误处理**：包含全局错误处理机制 (`errorHandler.js`)，确保应用稳定运行。
- **强大的数据服务**：模块化的数据服务，支持并行数据获取、自动认证和会话管理。
- **兜底机制**：在AI数据匹配失败时，自动采用原始提取结果，确保业务流程不中断。

## 系统要求

- **Node.js**: 18.0.0 或更高版本
- **操作系统**: Windows 64 位系统
- **微信客户端**: **必须使用 3.9.12.17 版本**
- **WechatFerry**: 需要单独安装并运行，作为微信客户端的通信桥梁。

## 快速开始

### 1. 安装并运行 WechatFerry

在运行本应用之前，必须先安装并启动 WechatFerry 服务。

1. 访问 [WechatFerry 官方文档](https://wcferry.netlify.app/) 并按照指南完成安装。
2. 确保 WechatFerry 服务在默认端口 `10086` 上成功运行。
3. 登录版本为 **3.9.12.17** 的微信客户端。

> **重要提示**: WechatFerry 服务必须在应用程序启动前运行，并且必须使用与之兼容的微信客户端版本。

### 2. 安装应用

1. 克隆或下载本项目：
   ```bash
   git clone <repository-url>
   cd wechat-listener
   ```
2. 安装项目依赖：
   ```bash
   npm install
   ```

### 3. 配置应用

在启动应用前，请仔细阅读下文的 **“配置深度解析”** 部分，并完成对 `.env` 文件和 `config/`
目录下所有相关文件的配置。

### 4. 启动应用

推荐使用管理员权限运行，以确保应用有足够权限进行文件操作和网络通信。

```bash
# 开发模式 (使用 nodemon 自动重启)
npm run dev

# 生产模式
npm start

# 使用管理员权限运行 (推荐)
npm run admin
```

应用启动后，请检查控制台输出，确保没有错误信息，并验证日志文件是否在 `logs/` 目录下正常生成。

---

## 配置深度解析

应用的灵活性高度依赖于 `.env` 文件和 `config/` 目录下的配置文件。

### 1. 环境变量 (`.env`)

此文件负责管理应用的敏感信息和基础环境设置。请从 `.env.example` 复制创建。

| 变量名                        | 说明                                                        | 示例                         |
| :---------------------------- | :---------------------------------------------------------- | :--------------------------- |
| `DEBUG`                       | 是否开启调试模式。开启后，日志会更详尽。                    | `false`                      |
| `LOG_LEVEL`                   | 文件日志的最低记录级别 (`error`, `warn`, `info`, `debug`)。 | `info`                       |
| `SERVER_URL`                  | 后端API服务器的基础地址。                                   | `http://0.0.0.0:3000/api/v1` |
| `SERVER_TIMEOUT`              | 请求后端API的超时时间（毫秒）。                             | `10000`                      |
| `SERVER_USERNAME`             | 登录后端服务器的用户名。                                    | `your_username`              |
| `SERVER_PASSWORD`             | 登录后端服务器的密码。                                      | `your_password`              |
| `SERVER_LOGIN_URL`            | 登录接口的相对路径。                                        | `/user/login`                |
| `SERVER_WECHAT_MSG_URL`       | 用于转发已处理微信消息的接口路径。                          | `/admin/wechatMsg`           |
| `SERVER_WECHAT_CROP_URL`      | 用于获取作物基准数据的接口路径。                            | `/data/crops`                |
| `SERVER_WECHAT_COMPANY_URL`   | 用于获取公司基准数据的接口路径。                            | `/data/companies`            |
| `SERVER_WECHAT_TRANSPORT_URL` | 用于获取厂车基准数据的接口路径。                            | `/data/transport`            |
| `MOONSHOT_API_KEY`            | Moonshot AI 的 API 密钥。                                   | `your_moonshot_api_key`      |
| `FILE_SAVE_DIR`               | 接收到的文件（如图片）的本地保存目录。                      | `./received_files`           |

### 2. 核心配置文件 (`config/`)

#### `contacts.json`

- **用途**：定义消息监听的白名单。只有来自该文件内 `wxid` 的消息才会被处理。
- **配置**：
  - `contacts`: 一个对象，键为用户或群聊的 `wxid`，值为一个数组，指定需要处理的消息类型（如 `text`,
    `image`, `unsupported`）。
- **示例**：
  ```json
  {
    "contacts": {
      "wxid_abc123": ["text", "image"],
      "12345678@chatroom": ["text", "image"]
    }
  }
  ```

#### `keywords.json` 和 `priorities.json`

- **用途**：共同实现消息的分类。系统根据消息内容是否包含 `keywords.json`
  中定义的关键词来确定其业务类型，并使用 `priorities.json` 解决多重匹配时的冲突。
- **`keywords.json`**：定义每种业务类型关联的关键词。

  ```json
  {
    "transportKeywords": ["厂车", "物流"],
    "ureaKeywords": ["尿素", "车用尿素"]
  }
  ```

- **`priorities.json`**：定义业务类型的优先级。**数组中越靠前的类型，优先级越高**。

  ```json
  ["transport", "urea", "normal"]
  ```

  _例：如果一条消息同时包含“厂车”和“尿素”，根据此配置，它将被判定为 `transport` 类型。_

#### `aiProcessing.json`

- **用途**：控制不同业务类型的消息是否启用AI处理。
- **配置**：
  - `enabled`: 全局开关，`false` 则所有类型都不使用AI。
  - `typeSettings`: 各类型的独立开关。

- **示例**：

  ```json
  {
    "enabled": true,
    "typeSettings": {
      "transport": true,
      "urea": true,
      "normal": false
    }
  }
  ```

  _例：以上配置中，`normal` 类型的消息将不会调用AI服务。_

#### `aliases.json`

- **用途**：定义别名到标准名称的映射。这是 **`DataMatcher`**
  进行数据验证前的关键步骤，用于将AI提取的非标准名称（如简称、俗称）统一为与数据库匹配的标准名称。
- **配置**：一个对象，键为别名，值为标准名。
- **示例**：

  ```json
  {
    "TDI": "甲苯二异氰酸酯",
    "MDI": "二苯基甲烷二异氰酸酯",
    "中化": "中化集团"
  }
  ```

  _例：如果AI从消息中提取出 "TDI"，`DataMatcher`
  会先将其转换为 "甲苯二异氰酸酯"，然后再与从服务器获取的作物列表进行模糊匹配。_

#### `prompts/` 目录

- **用途**：存放各类消息所使用的AI提示词模板（Markdown格式）。文件名必须与业务类型名称对应（如
  `transport_prompt.md`）。
- **机制**：系统会根据消息类型加载相应模板，并将消息内容、当前日期、以及从 `dataService`
  获取的各类业务数据（如公司、作物列表）填充进去，形成最终的提示词。

---

## 架构设计与核心流程

### 1. 核心组件职责

- **`ApplicationManager` (`src/app/applicationManager.js`)**: 应用的入口和总控制器。负责初始化
  `WechatferryAgent`，绑定消息监听器，并在接收到消息时将其递交给 `MessageHandler`。
- **`MessageHandler`
  (`src/core/messageHandler.js`)**: 消息处理的核心管道。它负责执行完整的处理流程：检查联系人白名单 -> 确定消息策略 -> 标准化消息内容 -> 调用AI服务 -> 验证并转发结果。
- **`Strategy` (`src/core/strategies.js`)**: 消息分类策略模块。根据 `keywords.json` 和
  `priorities.json` 的配置，判断传入消息的业务类型。
- **`AIService` (`src/services/aiService.js`)**: AI处理服务。负责根据消息类型加载并填充 `prompt`
  模板，调用Moonshot AI接口，并对返回的JSON数据进行初步解析和格式化。
- **`DataService` (`src/services/dataService.js`)**: 后端数据服务。在应用启动时，通过 `HttpClient`
  从服务器获取所有必要的业务基准数据（如公司、作物、厂车列表），为后续的数据匹配做准备。
- **`DataMatcher`
  (`src/utils/dataMatcher.js`)**: 数据匹配与验证器。这是确保数据质量的关键组件。它接收 `AIService`
  返回的数据，首先使用 `aliases.json` 进行名称标准化，然后通过模糊搜索算法与 `DataService`
  预加载的基准数据进行匹配，并为匹配成功的数据添加ID等信息。
- **`HttpClient` (`src/services/httpClient.js`)**: 一个封装了 `axios`
  的HTTP客户端。它统一管理所有对后端服务器的请求，并内置了自动登录、`token`管理和请求重试逻辑。

### 2. 业务流程图 (增强版)

下图展示了单条消息从接收到最终处理完毕的完整业务流程：

```mermaid
graph TD
    A[接收微信消息] --> B{发送人是否在白名单?};
    B -->|是| C{内容是否含关键词?};
    B -->|否| Z[忽略消息];
    C -->|是| D[根据优先级确定消息类型];
    C -->|否| Z;
    D --> E{判断具体消息类型};
    E -->|文本| F[直接使用文本内容];
    E -->|图片| G[下载图片 -> AI提取文字];
    E -->|公众号文章| H[抓取文章内容];
    F --> I[内容标准化];
    G --> I;
    H --> I;
    I --> J[选择提示词模板 -> 填充业务数据 -> 调用AI服务];
    J --> K[AI返回结构化数据];
    K --> L[DataMatcher: 标准化(aliases.json) -> 模糊匹配];
    L -->|匹配成功| M[丰富数据(添加ID)];
    L -->|匹配失败| N[启用兜底机制(标记_fallback)];
    M --> O[将最终数据发送到服务器];
    N --> O;
    O --> P[记录日志 -> 结束];
```

---

## 自定义与扩展

系统设计高度灵活，支持在不修改核心代码的情况下进行功能扩展。

### 添加新的消息类型

推荐使用内置的命令行工具，它可以自动完成所有相关配置文件的修改：

```bash
# 语法: node src/utils/addMessageType.js <类型名称> [关键词列表] [优先级] [是否启用AI]
# 示例: 添加一个处理价格信息的新类型，优先级为30
node src/utils/addMessageType.js price "价格,报价,行情" 30 true
```

此命令会自动执行以下操作：

1. 在 `config/keywords.json` 中添加 `priceKeywords`。
2. 在 `config/priorities.json` 中根据指定的优先级插入 `price`。
3. 在 `config/aiProcessing.json` 中添加 `price` 的AI开关。
4. 在 `config/prompts/` 目录下创建一个新的提示词模板文件 `price_prompt.md`。

之后，您只需编辑新生成的 `price_prompt.md` 文件，定义好希望AI提取的数据结构即可。

---

## 项目结构

wechat-listener/ ├── config/ # 存放所有用户可配置的文件 │ ├──
aiProcessing.json # 控制不同消息类型是否启用AI处理的开关 │ ├──
aliases.json # 定义别名到标准名称的映射，用于数据清洗和匹配 │ ├──
contacts.json # 配置需要监听的联系人（个人或群聊）的白名单 │ ├──
keywords.json # 定义不同业务类型关联的关键词 │ ├── priorities.json # 定义消息类型的处理优先级 │ └──
prompts/ # 存放用于不同消息类型的AI提示词模板（.md文件）├── src/ # 应用程序的全部源代码 │ ├──
app/ # 负责应用的核心启动和生命周期管理 │ │ ├── applicationManager.js # 初始化并管理WechatFerry
Agent实例 │ │ ├── main.js # 应用主逻辑入口，编排各项服务 │ │ └── start.js # 应用启动脚本 │ ├──
core/ # 存放核心业务处理逻辑 │ │ ├── configLoader.js # 加载并验证所有配置文件 │ │ ├──
messageHandler.js # 消息处理的总调度管道 │ │ ├──
strategies.js # 根据关键词和优先级确定消息处理策略 │ │ └── prompts/ # 动态构建和管理AI提示词的模块 │
├── services/ # 用于与所有外部服务进行交互 │ │ ├── aiService.js # 封装对Moonshot AI接口的调用 │ │
├── apiConfig.js # 统一定义所有后端API的端点 │ │ ├── articleFetcher.js # 抓取公众号文章的HTML内容 │
│ ├── articleService.js # 编排文章抓取和内容提取服务 │ │ ├──
dataService.js # 从后端API获取并缓存业务数据（如公司、作物列表）│ │ ├──
httpClient.js # 封装axios，提供自动认证和会话管理的HTTP客户端 │ │ └──
imageDownloader.js # 从消息中下载图片文件 │ └── utils/ # 存放各类可重用的工具函数 │ ├──
addMessageType.js # 用于快速添加新消息类型的命令行工具 │ ├── config.js # 提供便捷的配置项访问接口 │
├── dataMatcher.js # 实现别名转换和模糊匹配的核心工具 │ ├── errorHandler.js # 全局错误处理程序 │ ├──
fileUtils.js # 文件系统操作相关的辅助函数 │ ├── logUtils.js # 封装并统一日志接口，供整个应用调用 │
└── logger.js # 基于winston的文件日志记录器实现 │ ├──
messageUtils.js # 处理和解析微信消息对象的辅助函数 │ ├── validators.js # 数据验证函数 │ └──
warnings.js # 自定义警告处理 ├── logs/ # 存放应用运行过程中生成的日志文件 └──
received_files/ # 存放从微信接收并下载的图片等文件

---

## 故障排除 (FAQ)

1. **错误: `Connect tcp://127.0.0.1:10086 failed: Connection refused`**
   - **原因**: WechatFerry 服务未启动或未在指定端口运行。
   - **解决**: 确保 WechatFerry 服务已成功启动，并检查微信客户端 (3.9.12.17) 是否已登录。

2. **数据匹配不准确或失败**
   - **原因**: AI提取的实体名称与数据库中的标准名不一致，且未配置别名。
   - **解决**: 在 `config/aliases.json`
     文件中添加新的别名映射。例如，如果AI经常返回 "中化集团公司"，而数据库中是 "中化集团"，则应添加
     `"中化集团公司": "中化集团"`。

3. **AI 处理失败或结果异常**
   - **解决**:
     1. 检查 `.env` 文件中的 `MOONSHOT_API_KEY` 是否正确且有效。
     2. 检查对应的提示词模板文件是否存在于 `config/prompts/` 目录且内容无误。
     3. 检查网络连接是否可以访问 Moonshot AI 服务器。
     4. 查看 `logs/error-*.log` 获取详细错误信息。

4. **收不到任何消息**
   - **解决**:
     1. 确认微信客户端已登录且 WechatFerry 运行正常。
     2. 检查 `config/contacts.json` 中是否正确配置了目标联系人的 `wxid`。
     3. 查看 `logs/wcf.txt` 和 `logs/message-*.log` 以排查问题。

---

## 参考资料

- **WeChatFerry**:
  - [官方文档](https://wcferry.netlify.app/)
  - [GitHub 仓库](https://github.com/wechatferry/wechatferry)
- **@wechatferry/agent**:
  - [API 文档](https://www.jsdocs.io/package/@wechatferry/agent)
  - [NPM 包](https://www.npmjs.com/package/@wechatferry/agent)
- **AI 服务**:
  - [Moonshot AI 开放平台](https://platform.moonshot.cn/)
- **核心依赖库**:
  - [winston](https://github.com/winstonjs/winston): 一个功能强大的通用日志库。
  - [axios](https://github.com/axios/axios): 广受欢迎的基于 Promise 的 HTTP 客户端。

## 许可说明

本项目仅供学习和交流使用，请严格遵守相关法律法规和微信使用条款，禁止用于任何非法用途。

---

## 代码规范

为了保持代码库的整洁和一致性，本项目集成了 `ESLint` 和 `Prettier`。

- **`npm run lint`**: 检查整个项目的代码规范。
- **`npm run lint:fix`**: 自动修复大部分代码规范问题。
- **`npm run format`**: 使用 Prettier 格式化所有代码文件。

强烈建议在提交代码前运行 `npm run lint:fix` 和 `npm run format`，以确保代码风格统一。

## 生产环境部署

对于生产环境，推荐使用 `pm2`
来管理应用，它可以提供进程守护、日志管理、性能监控和负载均衡等高级功能。

### 1. 安装 pm2

```bash
npm install pm2 -g
```

### 2. 使用 pm2 启动应用

直接使用 `npm start` 命令启动应用：

```bash
pm2 start npm --name "wechat-listener" -- run start
```

- `--name "wechat-listener"`: 为你的应用进程指定一个名称，方便管理。
- `-- run start`: 告诉 `pm2` 执行 `package.json` 中定义的 `start` 脚本。

### 3. 管理应用

- **查看应用列表**: `pm2 list`
- **监控应用日志**: `pm2 logs wechat-listener`
- **停止应用**: `pm2 stop wechat-listener`
- **重启应用**: `pm2 restart wechat-listener`
- **删除应用**: `pm2 delete wechat-listener`

### 4. 开机自启

`pm2` 可以生成并配置一个启动脚本，让应用在服务器重启后自动运行。

```bash
pm2 startup
# 根据提示执行输出的命令
pm2 save
```
