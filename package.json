{"name": "wechat-listener", "version": "1.0.0", "description": "基于@wechatferry/agent的微信消息监听应用", "main": "index.js", "type": "module", "bin": "src/app/start.js", "scripts": {"start": "node -r dotenv/config src/app/start.js dotenv_config_path=.env.production", "dev": "nodemon", "launch": "node launcher.js", "launch:dev": "node launcher.js 2", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write ."}, "keywords": ["wechat", "wechatferry", "bot"], "author": "shiyuanzheng", "license": "MIT", "dependencies": {"@wechatferry/agent": "^0.0.26", "axios": "^1.11.0", "axios-retry": "^4.5.0", "chalk": "^4.1.2", "cheerio": "^1.1.2", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "file-box": "^1.4.15", "openai": "^5.10.2", "ora": "^8.2.0", "sharp": "^0.34.3", "tough-cookie": "^5.1.2", "tough-cookie-file-store": "^3.2.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/node": "^7.28.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.28.0", "chai": "^5.2.1", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "nodemon": "^3.1.10", "prettier": "^3.6.2"}, "engines": {"node": ">=18.0.0"}}