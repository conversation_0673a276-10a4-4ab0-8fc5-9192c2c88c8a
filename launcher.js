#!/usr/bin/env node
import { spawn } from 'child_process'
import { existsSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __dirname = dirname(fileURLToPath(import.meta.url))

// 彩色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
}

const log = {
  info: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.blue}${msg}${colors.reset}`)
}

// 执行命令的Promise封装
function execCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { stdio: 'pipe' })
    let stdout = ''
    let stderr = ''
    
    child.stdout.on('data', (data) => {
      stdout += data.toString()
    })
    
    child.stderr.on('data', (data) => {
      stderr += data.toString()
    })
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(stdout.trim())
      } else {
        reject(new Error(stderr || `命令执行失败，退出码: ${code}`))
      }
    })
  })
}

// 检查命令是否存在
async function checkCommand(command) {
  try {
    await execCommand(command, ['--version'])
    return true
  } catch {
    return false
  }
}

// 主启动函数
async function startApp() {
  console.log()
  log.info('========================================')
  log.info('    微信消息监听器 - 一键启动工具')
  log.info('========================================')
  console.log()

  try {
    // 1. 检查Node.js
    log.step('[1/6] 检查Node.js环境...')
    if (!(await checkCommand('node'))) {
      log.error('❌ 错误：未检测到 Node.js')
      log.error('请安装 Node.js 18.0.0 或更高版本')
      log.warning('下载地址：https://nodejs.org/')
      process.exit(1)
    }
    
    const nodeVersion = await execCommand('node', ['--version'])
    log.success(`✅ Node.js版本: ${nodeVersion}`)

    // 检查Node.js版本
    const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0])
    if (majorVersion < 18) {
      log.error('❌ 错误：Node.js 版本过低')
      log.error(`当前版本：${nodeVersion}，需要：v18.0.0 或更高版本`)
      log.warning('请升级 Node.js：https://nodejs.org/')
      process.exit(1)
    }
    log.success('✅ Node.js版本符合要求')

    // 2. 检查npm
    log.step('[2/6] 检查npm环境...')
    if (!(await checkCommand('npm'))) {
      log.error('❌ 错误：npm 不可用')
      log.error('请重新安装 Node.js')
      process.exit(1)
    }
    
    const npmVersion = await execCommand('npm', ['--version'])
    log.success(`✅ npm版本: ${npmVersion}`)

    // 3. 检查项目文件
    log.step('[3/6] 检查项目文件...')
    if (!existsSync('package.json')) {
      log.error('❌ 错误：当前目录不是项目根目录')
      log.error('请将此脚本放在项目根目录中运行')
      process.exit(1)
    }
    
    if (!existsSync('src/app/start.js')) {
      log.error('❌ 错误：缺少启动文件')
      log.error('请确保项目文件完整')
      process.exit(1)
    }
    
    log.success('✅ 项目文件检查完成')

    // 4. 检查并安装依赖
    log.step('[4/6] 检查项目依赖...')
    if (!existsSync('node_modules')) {
      log.info('📦 正在安装项目依赖...')
      await execCommand('npm', ['install'])
      log.success('✅ 依赖安装完成')
    } else {
      log.success('✅ 依赖已安装')
    }

    // 5. 检查环境配置
    log.step('[5/6] 检查环境配置...')
    if (!existsSync('.env') && !existsSync('.env.production')) {
      log.error('❌ 错误：缺少环境配置文件')
      log.error('请根据 .env.example 创建 .env 或 .env.production 文件')
      log.error('并配置必要参数（服务器地址、认证信息、AI 密钥等）')
      process.exit(1)
    }
    
    log.success('✅ 环境配置文件存在')

    // 6. 选择运行模式
    log.step('[6/6] 选择运行模式...')
    console.log()
    log.info('请选择运行模式:')
    console.log('1. 生产模式 (推荐)')
    console.log('2. 开发模式 (调试用)')
    console.log()

    // 简单的用户输入处理
    const mode = process.argv[2] || '1'
    
    if (mode === '1') {
      console.log()
      log.success('🚀 正在启动生产模式...')
      console.log()
      const child = spawn('npm', ['start'], { stdio: 'inherit' })
      
      child.on('close', (code) => {
        console.log()
        log.info(`程序已退出，退出码: ${code}`)
      })
    } else if (mode === '2') {
      console.log()
      log.info('🛠️ 正在启动开发模式...')
      console.log()
      const child = spawn('npm', ['run', 'dev'], { stdio: 'inherit' })
      
      child.on('close', (code) => {
        console.log()
        log.info(`程序已退出，退出码: ${code}`)
      })
    } else {
      console.log()
      log.warning('❌ 无效选项，将使用默认的生产模式')
      console.log()
      log.success('🚀 正在启动生产模式...')
      console.log()
      const child = spawn('npm', ['start'], { stdio: 'inherit' })
      
      child.on('close', (code) => {
        console.log()
        log.info(`程序已退出，退出码: ${code}`)
      })
    }

  } catch (error) {
    log.error(`❌ 启动失败：${error.message}`)
    process.exit(1)
  }
}

// 运行启动程序
startApp()