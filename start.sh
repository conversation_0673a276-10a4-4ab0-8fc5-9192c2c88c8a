#!/bin/bash
# 微信消息监听器 - Linux/macOS启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${CYAN}$1${NC}"
}

log_success() {
    echo -e "${GREEN}$1${NC}"
}

log_error() {
    echo -e "${RED}$1${NC}"
}

log_warning() {
    echo -e "${YELLOW}$1${NC}"
}

log_step() {
    echo -e "${BLUE}$1${NC}"
}

# 检查命令是否存在
check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

echo
log_info "========================================"
log_info "    微信消息监听器 - 一键启动工具"
log_info "========================================"
echo

# 1. 检查Node.js
log_step "[1/6] 检查Node.js环境..."
if ! check_command node; then
    log_error "❌ 错误：未检测到 Node.js"
    log_error "请安装 Node.js 18.0.0 或更高版本"
    log_warning "下载地址：https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
log_success "✅ Node.js版本: $NODE_VERSION"

# 检查Node.js版本
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
    log_error "❌ 错误：Node.js 版本过低"
    log_error "当前版本：$NODE_VERSION，需要：v18.0.0 或更高版本"
    log_warning "请升级 Node.js：https://nodejs.org/"
    exit 1
fi
log_success "✅ Node.js版本符合要求"

# 2. 检查npm
log_step "[2/6] 检查npm环境..."
if ! check_command npm; then
    log_error "❌ 错误：npm 不可用"
    log_error "请重新安装 Node.js"
    exit 1
fi

NPM_VERSION=$(npm --version)
log_success "✅ npm版本: $NPM_VERSION"

# 3. 检查项目文件
log_step "[3/6] 检查项目文件..."
if [ ! -f "package.json" ]; then
    log_error "❌ 错误：当前目录不是项目根目录"
    log_error "请将此脚本放在项目根目录中运行"
    exit 1
fi

if [ ! -f "src/app/start.js" ]; then
    log_error "❌ 错误：缺少启动文件"
    log_error "请确保项目文件完整"
    exit 1
fi

log_success "✅ 项目文件检查完成"

# 4. 检查并安装依赖
log_step "[4/6] 检查项目依赖..."
if [ ! -d "node_modules" ]; then
    log_info "📦 正在安装项目依赖..."
    npm install
    if [ $? -ne 0 ]; then
        log_error "❌ 错误：依赖安装失败"
        log_error "请检查网络连接或删除node_modules文件夹后重试"
        exit 1
    fi
    log_success "✅ 依赖安装完成"
else
    log_success "✅ 依赖已安装"
fi

# 5. 检查环境配置
log_step "[5/6] 检查环境配置..."
if [ ! -f ".env" ] && [ ! -f ".env.production" ]; then
    log_error "❌ 错误：缺少环境配置文件"
    log_error "请根据 .env.example 创建 .env 或 .env.production 文件"
    log_error "并配置必要参数（服务器地址、认证信息、AI 密钥等）"
    exit 1
fi

log_success "✅ 环境配置文件存在"

# 6. 选择运行模式
log_step "[6/6] 选择运行模式..."
echo
log_info "请选择运行模式:"
echo "1. 生产模式 (推荐)"
echo "2. 开发模式 (调试用)"
echo
read -p "请输入选项 (1-2): " mode

case $mode in
    1)
        echo
        log_success "🚀 正在启动生产模式..."
        echo
        npm start
        ;;
    2)
        echo
        log_info "🛠️ 正在启动开发模式..."
        echo
        npm run dev
        ;;
    *)
        echo
        log_warning "❌ 无效选项，将使用默认的生产模式"
        echo
        log_success "🚀 正在启动生产模式..."
        echo
        npm start
        ;;
esac

echo
log_info "程序已退出"