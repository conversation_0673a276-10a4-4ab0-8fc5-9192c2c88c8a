/**
 * 数据匹配工具 - 高性能公司名称和作物名称匹配
 * 优化版 - 支持纯名称匹配和文本内容匹配两种模式
 *
 * @version 4.0
 * <AUTHOR>
 * @date 2025-07-25
 *
 * 优化说明:
 * 1. **性能优化**:
 *    - 使用Map索引提高查找效率
 *    - 优化字符串匹配算法，减少不必要计算
 *    - 添加早期退出机制，提升匹配速度
 *
 * 2. **功能扩展**:
 *    - 支持纯公司名称匹配（原有模式）
 *    - 支持文本内容匹配（新增模式）
 *    - 自动识别输入类型，智能选择匹配策略
 *
 * 3. **算法优化**:
 *    - 改进相似度计算，提高准确率
 *    - 优化匹配阈值和权重分配
 *    - 减少误匹配和漏匹配
 */

import { isValidArray } from './dataValidators.js'

// 优化后的配置参数
const CONFIG = {
  SIMILAR_THRESHOLD: 0.75, // 提高阈值，减少误匹配
  COMPANY: {
    REMOVE_SUFFIXES: [
      '有限公司',
      '有限责任公司',
      '股份有限公司',
      '公司',
      '集团',
      '股份',
      '有限责任',
      '企业',
      '厂',
    ],
    MIN_LENGTH: 2,
    MAX_LENGTH: 30,
    // 新增：常见公司名称模式
    COMMON_PATTERNS: [
      /(.+?)(?:有限公司|有限责任公司|股份有限公司|公司|集团|股份|企业|厂)$/,
      /(.+?)(?:\(.*?\)|\（.*?\）)/, // 括号内容
    ],
  },
  CROP: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 20,
  },
  // 新增：文本匹配配置
  TEXT_MATCH: {
    MAX_SCAN_LENGTH: 200, // 限制扫描长度，提高性能
    COMPANY_INDICATORS: ['公司', '集团', '企业', '厂', '有限', '股份'], // 公司名称指示词
  },
}

/**
 * DataMatcher类 - 高性能字符串匹配和数据匹配功能
 */
class DataMatcher {
  // 缓存索引，提高重复查询性能
  static _companyIndexCache = new Map()
  static _cropIndexCache = new Map()

  /**
   * 检测输入类型：纯名称 vs 包含名称的文本
   * @param {string} input - 输入字符串
   * @returns {string} - 'name' | 'text'
   */
  static detectInputType(input) {
    if (!input || typeof input !== 'string') return 'name'

    const trimmed = input.trim()

    // 包含句子分隔符，视为文本
    if (/[。！？；\n，]/.test(trimmed)) {
      return 'text'
    }

    // 包含常见文本词汇，视为文本
    const textIndicators = [
      '我们',
      '公司是',
      '今日',
      '发布',
      '宣布',
      '请联系',
      '专业从事',
      '股价',
      '投资者',
    ]
    if (textIndicators.some(indicator => trimmed.includes(indicator))) {
      return 'text'
    }

    // 如果包含公司指示词且长度较长，可能是文本
    const hasCompanyIndicators = CONFIG.TEXT_MATCH.COMPANY_INDICATORS.some(indicator =>
      trimmed.includes(indicator),
    )

    // 长度超过20字符且包含指示词，视为文本
    if (trimmed.length > 20 && hasCompanyIndicators) {
      return 'text'
    }

    return 'name'
  }

  /**
   * 从文本中提取可能的公司名称
   * @param {string} text - 输入文本
   * @returns {Array<string>} - 提取的公司名称数组
   */
  static extractCompanyNames(text) {
    if (!text || typeof text !== 'string') return []

    // 限制扫描长度，提高性能
    const scanText = text.substring(0, CONFIG.TEXT_MATCH.MAX_SCAN_LENGTH)
    const candidates = []

    // 方法1: 使用正则表达式提取完整公司名称
    const companyPatterns = [
      /([^\s，。！？；]{2,20}(?:有限公司|有限责任公司|股份有限公司|公司|集团|企业|厂))/g,
      /([^\s，。！？；]{2,15}(?:\([^)]*\)|\（[^）]*\）)(?:有限公司|公司|集团|企业|厂)?)/g,
    ]

    for (const pattern of companyPatterns) {
      let match
      while ((match = pattern.exec(scanText)) !== null) {
        const candidate = match[1].trim()
        if (candidate && this.isValidCompanyName(candidate)) {
          candidates.push(candidate)
        }
      }
    }

    // 方法2: 查找包含公司指示词的词组
    const segments = scanText.split(/[，。！？；\n]/)
    for (const segment of segments) {
      const words = segment.trim().split(/[\s]+/)

      for (let i = 0; i < words.length; i++) {
        const word = words[i]

        // 如果当前词包含公司指示词
        if (CONFIG.TEXT_MATCH.COMPANY_INDICATORS.some(indicator => word.includes(indicator))) {
          // 尝试向前扩展形成完整公司名
          for (let j = Math.max(0, i - 3); j <= i; j++) {
            const candidate = words.slice(j, i + 1).join('')
            if (
              candidate.length >= 4 &&
              candidate.length <= 30 &&
              this.isValidCompanyName(candidate)
            ) {
              candidates.push(candidate)
            }
          }

          // 尝试向后扩展
          for (let k = i + 1; k <= Math.min(words.length - 1, i + 2); k++) {
            const candidate = words.slice(i, k + 1).join('')
            if (
              candidate.length >= 4 &&
              candidate.length <= 30 &&
              this.isValidCompanyName(candidate)
            ) {
              candidates.push(candidate)
            }
          }
        }
      }
    }

    // 方法3: 查找被引号或特殊符号包围的公司名
    const quotedPatterns = [
      /"([^"]{4,20}(?:公司|集团|企业|厂))"/g,
      /「([^」]{4,20}(?:公司|集团|企业|厂))」/g,
    ]

    for (const pattern of quotedPatterns) {
      let match
      while ((match = pattern.exec(scanText)) !== null) {
        const candidate = match[1].trim()
        if (this.isValidCompanyName(candidate)) {
          candidates.push(candidate)
        }
      }
    }

    // 去重并按长度排序（优先返回较完整的名称）
    const uniqueCandidates = [...new Set(candidates)]
    return uniqueCandidates.sort((a, b) => b.length - a.length)
  }

  /**
   * 优化的Levenshtein距离计算（带早期退出）
   * @param {string} s1 字符串1
   * @param {string} s2 字符串2
   * @param {number} maxDistance 最大距离阈值
   * @returns {number} 编辑距离
   */
  static levenshteinDistance(s1, s2, maxDistance = Infinity) {
    const len1 = s1.length
    const len2 = s2.length

    // 早期退出：长度差异过大
    if (Math.abs(len1 - len2) > maxDistance) {
      return maxDistance + 1
    }

    // 优化：使用滚动数组减少内存使用
    let prev = Array(len2 + 1)
      .fill(0)
      .map((_, i) => i)
    let curr = Array(len2 + 1).fill(0)

    for (let i = 1; i <= len1; i++) {
      curr[0] = i
      let minInRow = i

      for (let j = 1; j <= len2; j++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1
        curr[j] = Math.min(
          prev[j] + 1, // deletion
          curr[j - 1] + 1, // insertion
          prev[j - 1] + cost, // substitution
        )
        minInRow = Math.min(minInRow, curr[j])
      }

      // 早期退出：当前行最小值已超过阈值
      if (minInRow > maxDistance) {
        return maxDistance + 1
      }

      // 交换数组
      ;[prev, curr] = [curr, prev]
    }

    return prev[len2]
  }

  /**
   * 优化的字符串相似度计算
   * @param {string} str1 - 第一个字符串
   * @param {string} str2 - 第二个字符串
   * @param {Object} options - 计算选项
   * @returns {number} - 相似度分数（0-1之间，1表示完全匹配）
   */
  static stringSimilarity(str1, str2, options = {}) {
    if (!str1 || !str2) return 0

    const normalizedStr1 = this.normalizeString(str1)
    const normalizedStr2 = this.normalizeString(str2)

    // 完全匹配
    if (normalizedStr1 === normalizedStr2) return 1

    const maxLen = Math.max(normalizedStr1.length, normalizedStr2.length)
    if (maxLen === 0) return 1

    // 早期退出：长度差异过大
    const lengthDiff = Math.abs(normalizedStr1.length - normalizedStr2.length)
    if (lengthDiff / maxLen > 0.5) return 0

    // 子串匹配加分
    const minLen = Math.min(normalizedStr1.length, normalizedStr2.length)
    let substringBonus = 0
    if (normalizedStr1.includes(normalizedStr2) || normalizedStr2.includes(normalizedStr1)) {
      substringBonus = (minLen / maxLen) * 0.3 // 子串匹配给予30%的额外分数
    }

    // 计算编辑距离（带阈值优化）
    const maxAllowedDistance = Math.floor(maxLen * 0.4) // 允许40%的差异
    const distance = this.levenshteinDistance(normalizedStr1, normalizedStr2, maxAllowedDistance)

    if (distance > maxAllowedDistance) return 0

    const baseSimilarity = 1 - distance / maxLen
    return Math.min(baseSimilarity + substringBonus, 1)
  }

  /**
   * 字符串标准化处理 - 针对中国公司名称优化
   * @param {string} str - 输入字符串
   * @param {boolean} removeParentheses - 是否移除括号
   * @returns {string} - 标准化后的字符串
   */
  static normalizeString(str, removeParentheses = true) {
    if (!str) {
      return ''
    }

    let result = String(str)
      .toLowerCase()
      .replace(/[\s\-_\.]/g, '') // 移除空格、连字符、下划线、点号
      .replace(/[,，、]/g, '') // 移除常见分隔符
      .replace(/[""'']/g, '') // 移除引号
      .trim()

    if (removeParentheses) {
      result = result.replace(/[（）()]/g, '') // 移除括号
    }

    // 移除公司常见后缀
    for (const suffix of CONFIG.COMPANY.REMOVE_SUFFIXES) {
      result = result.replace(new RegExp(`${suffix}$`, 'g'), '') // 只移除末尾的后缀
    }

    return result
  }

  /**
   * 去除括号及括号内内容（仅用于前缀/子串辅助判断，不影响标准化主流程）
   * @param {string} str
   * @returns {string}
   */
  static removeParenthesesContent(str) {
    if (!str) {
      return ''
    }
    return String(str).replace(/（.*?）|\(.*?\)/g, '')
  }

  /**
   * 移除公司名称后缀，获取核心名称
   * @param {string} companyName - 公司名称
   * @returns {string} - 核心名称
   */
  static removeCompanySuffixes(companyName) {
    if (!companyName) return ''

    let coreName = companyName.trim()

    // 按长度排序，优先移除较长的后缀
    const sortedSuffixes = [...CONFIG.COMPANY.REMOVE_SUFFIXES].sort((a, b) => b.length - a.length)

    for (const suffix of sortedSuffixes) {
      if (coreName.endsWith(suffix)) {
        coreName = coreName.substring(0, coreName.length - suffix.length).trim()
        break // 只移除一个后缀
      }
    }

    return coreName
  }

  /**
   * 创建优化的匹配索引（支持缓存）
   * @param {Array} dataSet - 数据集数组
   * @param {string} nameField - 名称字段
   * @param {string} cacheKey - 缓存键
   * @returns {Map} - 索引Map
   */
  static createOptimizedIndex(dataSet, nameField = 'name', cacheKey = null) {
    // 检查缓存
    if (cacheKey && this._companyIndexCache.has(cacheKey)) {
      return this._companyIndexCache.get(cacheKey)
    }

    const indexMap = new Map()
    if (!dataSet || !isValidArray(dataSet)) {
      return indexMap
    }

    for (const item of dataSet) {
      if (item[nameField]) {
        const originalName = item[nameField]
        const normalizedName = this.normalizeString(originalName)

        // 多重索引：原名、标准化名、去括号名
        indexMap.set(originalName, item)
        indexMap.set(normalizedName, item)

        // 去括号版本
        const nameWithoutParens = this.removeParenthesesContent(originalName)
        if (nameWithoutParens !== originalName) {
          indexMap.set(this.normalizeString(nameWithoutParens), item)
        }

        // 添加常见变体
        for (const suffix of CONFIG.COMPANY.REMOVE_SUFFIXES) {
          if (originalName.includes(suffix)) {
            const withoutSuffix = originalName.replace(suffix, '').trim()
            if (withoutSuffix.length >= CONFIG.COMPANY.MIN_LENGTH) {
              indexMap.set(this.normalizeString(withoutSuffix), item)
            }
          }
        }
      }
    }

    // 缓存结果
    if (cacheKey) {
      this._companyIndexCache.set(cacheKey, indexMap)
    }

    return indexMap
  }

  /**
   * 优化的最佳匹配查找算法
   * @param {string} text - 输入文本
   * @param {Array} dataSet - 数据集数组
   * @param {string} nameField - 名称字段
   * @param {number} threshold - 匹配阈值
   * @returns {Object|null} - {item: 匹配项, score: 相似度分数} 或 null
   */
  static findBestMatch(text, dataSet, nameField = 'name', threshold = CONFIG.SIMILAR_THRESHOLD) {
    if (!text || !dataSet || !dataSet.length) return null

    // 使用索引进行快速查找
    const indexMap = this.createOptimizedIndex(dataSet, nameField)
    const normalizedText = this.normalizeString(text)

    // 1. 尝试精确匹配（最快）
    const exactMatch = indexMap.get(text) || indexMap.get(normalizedText)
    if (exactMatch) {
      return { item: exactMatch, score: 1, exactMatch: true }
    }

    // 2. 尝试去括号匹配
    const textNoParens = this.removeParenthesesContent(text)
    if (textNoParens !== text) {
      const parenMatch = indexMap.get(this.normalizeString(textNoParens))
      if (parenMatch) {
        return { item: parenMatch, score: 0.99, exactMatch: false, substringRelation: true }
      }
    }

    // 3. 相似度匹配（较慢，但更准确）
    let bestMatch = null
    let bestScore = threshold

    // 优化：只对前N个最有希望的候选项进行详细计算
    const candidates = this._selectCandidates(text, dataSet, nameField, 20)

    for (const item of candidates) {
      const name = item[nameField]
      if (!name) continue

      // 快速预筛选：长度差异过大直接跳过
      if (Math.abs(text.length - name.length) > Math.max(text.length, name.length) * 0.6) {
        continue
      }

      const similarity = this.stringSimilarity(text, name)

      if (similarity > bestScore) {
        bestScore = similarity
        bestMatch = item

        // 早期退出：找到高分匹配
        if (similarity > 0.95) break
      }
    }

    if (bestMatch) {
      const finalNormalizedText = this.normalizeString(text)
      const finalNormalizedBestName = this.normalizeString(bestMatch[nameField])
      const hasSubstringRelation =
        finalNormalizedBestName.includes(finalNormalizedText) ||
        finalNormalizedText.includes(finalNormalizedBestName)

      return {
        item: bestMatch,
        score: bestScore,
        exactMatch: bestScore >= 0.99,
        substringRelation: hasSubstringRelation,
      }
    }

    return null
  }

  /**
   * 选择最有希望的候选项（性能优化）
   * @param {string} text - 输入文本
   * @param {Array} dataSet - 数据集
   * @param {string} nameField - 名称字段
   * @param {number} maxCandidates - 最大候选数量
   * @returns {Array} - 候选项数组
   */
  static _selectCandidates(text, dataSet, nameField, maxCandidates = 20) {
    const normalizedText = this.normalizeString(text)
    const textLength = normalizedText.length

    // 按长度相似性和首字符匹配进行预筛选
    const scored = dataSet
      .filter(item => item[nameField])
      .map(item => {
        const name = item[nameField]
        const normalizedName = this.normalizeString(name)

        // 长度相似性分数
        const lengthScore =
          1 -
          Math.abs(textLength - normalizedName.length) / Math.max(textLength, normalizedName.length)

        // 首字符匹配分数
        const firstCharScore = normalizedText[0] === normalizedName[0] ? 0.2 : 0

        // 包含关系分数
        const containsScore =
          normalizedText.includes(normalizedName) || normalizedName.includes(normalizedText)
            ? 0.3
            : 0

        return {
          item,
          preScore: lengthScore + firstCharScore + containsScore,
        }
      })
      .sort((a, b) => b.preScore - a.preScore)
      .slice(0, maxCandidates)
      .map(scored => scored.item)

    return scored
  }

  /**
   * 智能公司名称匹配（支持两种输入模式）
   * @param {string} input - 输入内容（纯公司名称或包含公司名称的文本）
   * @param {Array} companies - 公司数据集
   * @param {Object} options - 匹配选项
   * @param {number} options.threshold - 相似度阈值
   * @param {boolean} options.forceTextMode - 强制使用文本模式
   * @returns {Object} - 匹配结果
   */
  static matchCompany(input, companies, options = {}) {
    if (!input || typeof input !== 'string' || !companies || !companies.length) {
      return { matched: false, reason: 'INVALID_INPUT' }
    }

    const threshold = options.threshold || CONFIG.SIMILAR_THRESHOLD
    const forceTextMode = options.forceTextMode || false

    // 自动检测输入类型
    const inputType = forceTextMode ? 'text' : this.detectInputType(input)

    if (inputType === 'text') {
      // 文本模式：先提取公司名称，再进行匹配
      return this._matchCompanyFromText(input, companies, threshold)
    } else {
      // 名称模式：直接匹配公司名称
      return this._matchCompanyByName(input, companies, threshold)
    }
  }

  /**
   * 从文本中匹配公司名称 - 使用新的直接搜索策略
   * @param {string} text - 包含公司名称的文本
   * @param {Array} companies - 公司数据集
   * @param {number} threshold - 匹配阈值
   * @returns {Object} - 匹配结果
   */
  static _matchCompanyFromText(text, companies, threshold) {
    // 新策略：直接在文本中搜索匹配，不再依赖预提取
    const directSearchResult = this._searchCompanyInText(text, companies, threshold)

    if (directSearchResult.matched) {
      return directSearchResult
    }

    // 备用策略：如果直接搜索失败，尝试传统的提取+匹配方式
    const extractedNames = this.extractCompanyNames(text)

    if (extractedNames.length === 0) {
      return { matched: false, reason: 'NO_COMPANY_NAMES_FOUND' }
    }

    let bestResult = null
    let bestScore = threshold

    // 对每个提取的名称进行匹配
    for (const name of extractedNames) {
      const result = this._matchCompanyByName(name, companies, threshold)

      if (result.matched && result.score > bestScore) {
        bestScore = result.score
        bestResult = result
        bestResult.extractedName = name // 记录提取的名称
        bestResult.matchType = 'extracted_then_matched'

        // 如果找到高分匹配，提前退出
        if (result.score > 0.95) break
      }
    }

    return bestResult || { matched: false, reason: 'NO_MATCH_FROM_TEXT' }
  }

  /**
   * 新的文本搜索匹配方法 - 直接在文本中搜索公司名称
   * @param {string} text - 包含公司名称的文本
   * @param {Array} companies - 公司数据集
   * @param {number} threshold - 匹配阈值
   * @returns {Object} - 匹配结果
   */
  static _searchCompanyInText(text, companies, threshold) {
    if (!text || !companies || !companies.length) {
      return { matched: false, reason: 'INVALID_INPUT' }
    }

    const normalizedText = this.normalizeString(text)
    let bestMatch = null
    let bestScore = threshold

    // 按公司名称长度排序，优先匹配较长的名称（避免短名称误匹配）
    const sortedCompanies = [...companies].sort(
      (a, b) => (b.name?.length || 0) - (a.name?.length || 0),
    )

    for (const company of sortedCompanies) {
      const companyName = company.name
      if (!companyName) continue

      const normalizedCompanyName = this.normalizeString(companyName)

      // 方法1: 精确包含匹配 - 完整公司名称在文本中
      if (normalizedText.includes(normalizedCompanyName)) {
        return {
          matched: true,
          company,
          score: 1.0,
          exactMatch: true,
          matchType: 'exact_contains',
          matchedText: companyName,
        }
      }

      // 方法2: 核心名称匹配 - 去除后缀的核心名称在文本中
      const coreCompanyName = this.removeCompanySuffixes(normalizedCompanyName)
      if (coreCompanyName.length >= 3 && normalizedText.includes(coreCompanyName)) {
        const score = 0.95 // 核心匹配给高分但不是满分
        if (score > bestScore) {
          bestMatch = {
            matched: true,
            company,
            score,
            exactMatch: false,
            matchType: 'core_contains',
            matchedText: coreCompanyName,
          }
          bestScore = score
        }
      }

      // 方法3: 反向匹配 - 公司名称包含文本片段（处理简化名称）
      const textSegments = normalizedText
        .split(/[^\u4e00-\u9fa5a-zA-Z0-9]+/)
        .filter(seg => seg.length >= 3)
      for (const segment of textSegments) {
        if (normalizedCompanyName.includes(segment) && segment.length >= 4) {
          const score = 0.85 + (segment.length / normalizedCompanyName.length) * 0.1
          if (score > bestScore) {
            bestMatch = {
              matched: true,
              company,
              score,
              exactMatch: false,
              matchType: 'reverse_contains',
              matchedText: segment,
            }
            bestScore = score
          }
        }
      }

      // 方法4: 滑动窗口相似度匹配
      if (bestScore < 0.9) {
        // 只有在没有找到高分匹配时才进行相似度计算
        const windowSize = Math.min(normalizedCompanyName.length + 5, 20)
        const step = Math.max(1, Math.floor(windowSize / 3)) // 跳跃式搜索提高性能

        for (let i = 0; i <= normalizedText.length - windowSize; i += step) {
          const textSegment = normalizedText.substring(i, i + windowSize)
          const similarity = this.stringSimilarity(textSegment, normalizedCompanyName)

          if (similarity > bestScore) {
            bestMatch = {
              matched: true,
              company,
              score: similarity,
              exactMatch: false,
              matchType: 'similarity',
              matchedText: textSegment,
            }
            bestScore = similarity
          }
        }
      }

      // 早期退出：找到高分匹配就停止
      if (bestScore >= 0.98) break
    }

    return bestMatch || { matched: false, reason: 'NO_MATCH_IN_TEXT' }
  }

  /**
   * 直接匹配公司名称
   * @param {string} companyName - 公司名称
   * @param {Array} companies - 公司数据集
   * @param {number} threshold - 匹配阈值
   * @returns {Object} - 匹配结果
   */
  static _matchCompanyByName(companyName, companies, threshold) {
    if (!this.isValidCompanyName(companyName)) {
      return { matched: false, reason: 'INVALID_NAME' }
    }

    // 使用优化的匹配算法
    const matchResult = this.findBestMatch(companyName, companies, 'name', threshold)

    if (matchResult) {
      return {
        matched: true,
        company: matchResult.item,
        score: matchResult.score,
        exactMatch: matchResult.exactMatch,
        substringRelation: matchResult.substringRelation,
      }
    }

    return { matched: false, reason: 'NO_MATCH' }
  }

  /**
   * 优化的作物名称匹配
   * @param {string} cropName - 作物名称
   * @param {Array} crops - 作物数据集
   * @param {Object} options - 匹配选项
   * @param {number} options.threshold - 相似度阈值
   * @returns {Object} - 匹配结果
   */
  static matchCrop(cropName, crops, options = {}) {
    if (!this.isValidCropName(cropName)) {
      return { matched: false, reason: 'INVALID_NAME' }
    }

    const threshold = options.threshold || CONFIG.SIMILAR_THRESHOLD

    // 使用优化的匹配算法
    const matchResult = this.findBestMatch(cropName, crops, 'name', threshold)

    if (matchResult) {
      return {
        matched: true,
        crop: matchResult.item,
        score: matchResult.score,
        exactMatch: matchResult.exactMatch,
        substringRelation: matchResult.substringRelation,
      }
    }

    return { matched: false, reason: 'NO_MATCH' }
  }

  /**
   * 批量匹配公司名称（性能优化版）
   * @param {Array<string>} inputs - 输入数组
   * @param {Array} companies - 公司数据集
   * @param {Object} options - 匹配选项
   * @returns {Array} - 匹配结果数组
   */
  static batchMatchCompanies(inputs, companies, options = {}) {
    if (!inputs || !inputs.length || !companies || !companies.length) {
      return []
    }

    // 创建一次索引，重复使用
    const cacheKey = `companies_${companies.length}_${Date.now()}`
    this.createOptimizedIndex(companies, 'name', cacheKey)

    return inputs.map(input => this.matchCompany(input, companies, options))
  }

  /**
   * 清理缓存（内存管理）
   */
  static clearCache() {
    this._companyIndexCache.clear()
    this._cropIndexCache.clear()
  }

  /**
   * 获取匹配统计信息
   * @param {Array} results - 匹配结果数组
   * @returns {Object} - 统计信息
   */
  static getMatchStats(results) {
    if (!results || !results.length) {
      return { total: 0, matched: 0, exactMatches: 0, avgScore: 0 }
    }

    const matched = results.filter(r => r.matched)
    const exactMatches = matched.filter(r => r.exactMatch)
    const totalScore = matched.reduce((sum, r) => sum + (r.score || 0), 0)

    return {
      total: results.length,
      matched: matched.length,
      exactMatches: exactMatches.length,
      matchRate: matched.length / results.length,
      exactMatchRate: exactMatches.length / results.length,
      avgScore: matched.length > 0 ? totalScore / matched.length : 0,
    }
  }

  /**
   * 处理API返回的结果
   * @param {Array} apiResults - API返回的结果数组
   * @param {Array} crops - 作物数据集
   * @param {Array} companies - 公司数据集
   * @param {Object} options - 处理选项
   * @param {string} [options.companyField="company"] - API结果中代表公司名称的字段
   * @param {number} options.threshold - 匹配阈值
   * @returns {Array} - 处理后的结果
   */
  static processApiResults(apiResults, crops, companies, options = {}) {
    if (!apiResults || !apiResults.length) {
      return []
    }

    const {
      threshold = CONFIG.SIMILAR_THRESHOLD,
      companyField = 'company',
      aliases = {}, // 接收别名
    } = options

    return apiResults
      .map(item => {
        if (!item || typeof item !== 'object') {
          return null
        }

        const result = { ...item }
        let companyName = result[companyField]

        // 核心修改：在匹配前，先检查并应用别名
        if (companyName && aliases[companyName]) {
          companyName = aliases[companyName]
        }

        // 处理公司名称
        if (companyName && typeof companyName === 'string') {
          const companyMatch = this.matchCompany(companyName, companies, { threshold })

          if (companyMatch.matched) {
            result[companyField] = companyMatch.company.name // 使用标准名称覆盖原字段
            result.companyId = companyMatch.company.id
            result._companyMatchScore = companyMatch.score
            result._companyExactMatch = companyMatch.exactMatch
          } else {
            // 对于业务逻辑，可以选择保留原始值或设置为null
            // 这里选择保留原始值，由业务层决定如何处理
            result._companyUnmatched = true
          }
        }

        // 处理作物名称
        if (result.crop && typeof result.crop === 'string') {
          const cropMatch = this.matchCrop(result.crop, crops, { threshold })

          if (cropMatch.matched) {
            result.crop = cropMatch.crop.name
            result.cropId = cropMatch.crop.id
            result._cropMatchScore = cropMatch.score
            result._cropExactMatch = cropMatch.exactMatch
          } else {
            // 对于业务逻辑，可以选择保留原始值或设置为null
            result._cropUnmatched = true
          }
        }

        return result
      })
      .filter(Boolean) // 过滤null值
  }

  /**
   * 验证公司名称是否有效
   * @param {string} companyName - 公司名称
   * @returns {boolean} - 是否有效
   */
  static isValidCompanyName(companyName) {
    if (!companyName || typeof companyName !== 'string') {
      return false
    }
    if (/^\d+$/.test(companyName)) {
      return false
    } // 纯数字不是有效的公司名

    const length = companyName.trim().length
    if (length < CONFIG.COMPANY.MIN_LENGTH || length > CONFIG.COMPANY.MAX_LENGTH) {
      return false
    }

    return true
  }

  /**
   * 验证作物名称是否有效
   * @param {string} cropName - 作物名称
   * @returns {boolean} - 是否有效
   */
  static isValidCropName(cropName) {
    if (!cropName || typeof cropName !== 'string') {
      return false
    }
    if (/^\d+$/.test(cropName)) {
      return false
    } // 纯数字不是有效的作物名

    const length = cropName.trim().length
    if (length < CONFIG.CROP.MIN_LENGTH || length > CONFIG.CROP.MAX_LENGTH) {
      return false
    }

    return true
  }
}

export default DataMatcher
